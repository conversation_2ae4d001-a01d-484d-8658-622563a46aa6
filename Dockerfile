FROM ruby:3.3.7-bullseye

ARG RAILS_ENV
ARG GITHUB_TOKEN
ARG BUNDLE_GEMS__CONTRIBSYS__COM

# Datadog’s source code integration 
ARG DD_GIT_REPOSITORY_URL
ARG DD_GIT_COMMIT_SHA
ENV DD_GIT_REPOSITORY_URL=${DD_GIT_REPOSITORY_URL} 
ENV DD_GIT_COMMIT_SHA=${DD_GIT_COMMIT_SHA}

RUN sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt bullseye-pgdg main" > /etc/apt/sources.list.d/pgdg.list'
RUN wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add -

RUN apt -y update && apt install -y binutils curl git gnupg cmake \
  postgresql-client-14 supervisor tar tzdata apt-transport-https apt-utils \
  libjpeg-dev libpng-dev libxrender-dev libfontconfig1 graphviz libjemalloc2 \
  libgumbo-dev

# Detect architecture and symlink the 86_64 file path for M1 laptops
RUN ARCH=$(uname -m) && \
  if [ "$ARCH" = "aarch64" ]; then \
  mkdir -p /usr/lib/x86_64-linux-gnu && \
  ln -sf /usr/lib/aarch64-linux-gnu/libjemalloc.so.2 /usr/lib/x86_64-linux-gnu/libjemalloc.so.2; \
  fi
ENV LD_PRELOAD=/usr/lib/x86_64-linux-gnu/libjemalloc.so.2

RUN mkdir /rails_terraform_docker
COPY . /rails_terraform_docker
WORKDIR /rails_terraform_docker

# Install Node & JS dependencies
ENV PATH=/usr/local/node/bin:$PATH
RUN NODE_VERSION=$(cat /rails_terraform_docker/.nvmrc) && \
  curl -sL https://github.com/nodenv/node-build/archive/master.tar.gz | tar xz -C /tmp/ && \
  /tmp/node-build-master/bin/node-build "${NODE_VERSION}" /usr/local/node && \
  npm install -g yarn@1 && \
  rm -rf /tmp/node-build-master && \
  yarn install --immutable

# Install bundler & gems
COPY Gemfile.lock ./
ENV BUNDLE_PATH=/usr/local/bundle
RUN gem install bundler:$(cat Gemfile.lock | tail -1 | tr -d " ") && \
  bundle config --local deployment 'true' && \
  bundle config github.com $GITHUB_TOKEN && \
  bundle config gems.contribsys.com $BUNDLE_GEMS__CONTRIBSYS__COM && \
  (bundle check || bundle install -j$((`nproc`-1)))

RUN chmod a+x bin/rails bin/rake bin/bundle bin/vite
COPY docker/entrypoint.sh docker/entrypoint.sh

# NOTE:  Sets environment variables that are expected to exist when rails executes
#        but which are not otherwise necessary for asset precompilation.
#        SECRET_KEY_BASE is set to a non-empty value to avoid precompilation failure.
RUN SECRET_KEY_BASE_DUMMY=1 \
  SECRET_KEY_BASE='SECRET_KEY_BASE' \
  DEVISE_SECRET_KEY='DEVISE_SECRET_KEY' \
  FLIPPER_UI_SECRET='' \
  FLIPPER_SLACK_HOOK='' \
  RAILS_ENV=production \
  bin/rails assets:precompile

# Uploads asset sourcemaps to Datadog if we have a version set
#
ARG DATADOG_API_KEY
ARG DD_VERSION
RUN if [ "$DD_VERSION" != "" ]; then \
  npx datadog-ci sourcemaps upload public/assets \
  --service "ams" \
  --release-version "$DD_VERSION" \
  --minified-path-prefix "/assets" || true; \
  fi

# Sets development environment for bundler, this needs to happen after assets
# compiling
ARG BUILD_TYPE=prod
RUN if [ "$BUILD_TYPE" = "development" ]; then \
  echo "Setting up development specific version" && \
  bundle config unset deployment; \
  fi

RUN chmod +x docker/entrypoint.sh
ENTRYPOINT [ "docker/entrypoint.sh" ]

# Start the main process.
CMD ["start-web"]

EXPOSE 3002
