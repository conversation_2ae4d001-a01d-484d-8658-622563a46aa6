# These environment variables are needed to run the app locally.
# Copy these into a file named .env.development to have them loaded automatically.

# Database
AURORA_HOST=postgres
AURORA_PORT=5432
ABOVELENDING_DATABASE_USERNAME=abovelending
ABOVELENDING_DATABASE_PASSWORD=abovedev

PORT=3002

AMS_DATA_S3_BUCKET="above-lending-ams-data-bucket"
CONTRACT_DOCUMENTS_BACKUP_S3_BUCKET_NAME="contract_documents_bucket"
OFFER_EXPIRATION_DAYS=28

AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_SESSION_TOKEN=

SERVICE_LAYER_BASE_URL=http://localhost:3000

GDS_BASE_URL='https://gds-base-url.test'
GDS_AUTH_BASE_URL='https://gds-base-auth-url.test'
GDS_CLIENT_ID="gds-client-id"
GDS_SECRET="gds-client-secret"

BEYOND_SFTP_HOST="https://beyond.sft"
BEYOND_SFTP_USER="user"
BEYOND_SFTP_PRIVATE_KEY="key"

AMS_JTI_TOKEN=secure-uuid-token

REDIS_URL=redis://localhost:6379/1
REDIS_URI=redis://localhost:6379/0

DEVOPS_HOSTS=
GITHUB_TOKEN=
SENDGRID_VALIDATION_API_KEY=
FLIPPER_UI_SECRET=flipper
DISABLE_FARADAY_LOGGING=false

JWT_DOCUSIGN_EXT_APP_ID=

LANDER_BASE_URL=http://localhost:3000

# LoanPro - Loan Management API
LOANPRO_TOKEN=
LOANPRO_INSTANCE_ID=

# LoanPro - Secure Payments API
LOANPRO_PCIWALLET_SECRET=
LOANPRO_PCIWALLET_TOKEN=

# LoanPro - Webhooks API
LOANPRO_WEBHOOK_TOKEN=

# DocuSign
DOCUSIGN_BASE_URL=https://demo.docusign.net/restapi

## Direct License Applications
DOCUSIGN_ACCOUNT_ID=
DOCUSIGN_IMPERSONATED_USER_GUID=
DOCUSIGN_INTEGRATOR_CLIENT_ID=
DOCUSIGN_PEM=

## CRB Applications
DOCUSIGN_CRB_ACCOUNT_ID=
DOCUSIGN_CRB_IMPERSONATED_USER_GUID=
DOCUSIGN_CRB_INTEGRATOR_CLIENT_ID=
DOCUSIGN_CRB_PEM=

# Trust Pilot
TRUSTPILOT_BUSINESS_UNIT_ID=
TRUSTPILOT_AUTH_TOKEN=
TRUSTPILOT_IPL_TEMPLATE_ID=
TRUSTPILOT_API_KEY=
TRUSTPILOT_SENDER_EMAIL=<EMAIL>
TRUSTPILOT_ACCOUNT_ID=<EMAIL>
TRUSTPILOT_ACCOUNT_PASSWORD=
TRUSTPILOT_REPLY_TO=<EMAIL>
TRUSTPILOT_REDIRECT_URI=https://abovelending.com
TRUSTPILOT_LOCALE=en-US
TRUSTPILOT_TOKEN_URL=https://api.trustpilot.com/v1/oauth/oauth-business-users-for-applications
TRUSTPILOT_INVITATION_URL=https://invitations-api.trustpilot.com/v1/private/business-units

# Talkdesk: There's no test environment, so we have separate Landing Page Leads record lists
# set up for sandbox, staging, and production in Talkdesk
TALKDESK_AUTH_BASE_URL=https://abovelending.talkdeskid.com
TALKDESK_BASE_URL=https://api.talkdeskapp.com
TALKDESK_CLIENT_ID=094c296d01d3439ab813cc0d418876f7
TALKDESK_SECRET=
# These point to the sandbox lists
TALKDESK_DROPOFF_LIST_MAP={"Beyond Finance":"657bc70c-2db6-4d74-82c6-505533837c80","Five Lakes Law Group":"c2392353-59d8-43cc-a1a1-c5d6393ee4d9"}
TALKDESK_POST_OFFER_DROPOFF_LIST_MAP={"Beyond Finance":"f84a73b4-9279-4c8c-aa00-5bf9732570b0","Five Lakes Law Group":"b90efb73-bd47-40c2-8f84-4b974373e89c"}
TALKDESK_APPROVED_DROPOFF_LIST_MAP={"Beyond Finance":"e641d721-5c7f-4db2-aeac-ed220540a80f","Five Lakes Law Group":"21c4d41c-4957-4e92-8522-aa4f328dfd07"}

# Snowflake SQL REST API
SNOWFLAKE_BASE_URL=https://bha08429.us-east-1.snowflakecomputing.com
SNOWFLAKE_TIMEOUT=60
SNOWFLAKE_ACCOUNT=BHA08429
SNOWFLAKE_USER=AMS_TEST
SNOWFLAKE_DATABASE=AMS
SNOWFLAKE_SCHEMA=AMS_TEST
SNOWFLAKE_WAREHOUSE=API_WH_XSMALL
SNOWFLAKE_ROLE=AMS_TEST
# This is a dummy key that doesn't actually have access to Snowflake. If you need to make actual
# Snowflake calls in development, the private key for the AMS_TEST user can be found in bitwarden
# entry 'AMS - Snowflake Env Vars (sandbox/staging/dev)'
SNOWFLAKE_PRIVATE_KEY="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

PLAID_BASE_URL=https://sandbox.plaid.com
PLAID_CLIENT_ID=plaidClientId
PLAID_SECRET=plaidClientSecret

# Ocrolus API
OCROLUS_AUTH_BASE_URL=https://auth.ocrolus.com
OCROLUS_BASE_URL=https://api.ocrolus.com
OCROLUS_CLIENT_ID=toyVadrqqSwDG04bt7u2fIPKxR0MtN74
OCROLUS_SECRET=ocrolus-secret
OCROLUS_WEBHOOK_USERNAME=test-ocrolus-webhook-username
OCROLUS_WEBHOOK_PASSWORD=test-ocrolus-webhook-password

# PostGrid API
POSTGRID_BASE_URL=https://api.postgrid.com
POSTGRID_API_KEY=test-postgrid-api-key

# Devise
DEVISE_SECRET_KEY=22bb9fd63b2580cc8f490de91bae0f33c728430a7c99ae31387721b4575ab986f1a95408114013e1da4de6284d8ef449e71a9cd78f92c3f3ca9a81bfad862613

# Socure API
SOCURE_BASE_URL=https://sandbox.socure.com/api/3.0/
SOCURE_API_KEY=23531c96-8f26-40b1-b9aa-cde5cd53b876
SOCURE_WEBHOOK_TOKEN=0vYiDpUKaLmqXMCA6vA2z0v1huNfSwQts0M7KSPNB7M=
AMS_SLACK_BOT_OAUTH_TOKEN='test'
