---

# This YAML file serves as a living declaration of Environment Variables for this Application.
# It provides a common interface between Application Developers and DevOps.
# https://www.notion.so/Environment-Variables-bcbb3a0c9e1d40b1bc3f19e2d4337c8c
# https://abovelending.atlassian.net/wiki/spaces/PROD/pages/1642692650/Environment+Variables

global:
  - name: APPLICATION_MANAGEMENT_SYSTEM
    path: "/global/AMS_CONFIG"
    source: AWS Secrets Manager
    env_vars:
    - name: SIDEKIQ_DASHBOARD_USERNAME
      description: Username for Sidekiq Dashboard
      type: string
    - name: DEVOPS_HOSTS
      description: A list of authorized hosts, that our APP is going to "host" on. Read more https://edgeapi.rubyonrails.org/classes/ActionDispatch/HostAuthorization.html
      type: string
    - name: FLIPPER_UI_SECRET
      description: The secret string that is used as a prefix for flipper-ui route
      type: string
    - name: AMS_SLACK_BOT_OAUTH_TOKEN
      description: Oauth token for ams slack bot
      type: string
    - name: FLIPPER_SLACK_HOOK
      description: Slack hook for flipper notifications
      type: string
environmental:
  - name: APPLICATION_MANAGEMENT_SYSTEM
    path: "/$ENV/AMS_CONFIG"
    source: AWS Secrets Manager
    env_vars:
    - name: AMS_BASE_URL
      description: The base URL for the AMS service, including the protocol (e.g., https://api.abovelending.com).
    - name: AMS_JTI_TOKEN
      description: JTI Token injected into JWTs generated by the AMS
      type: string
    - name: AMS_DATA_S3_BUCKET
      description: Name of ams data S3 Bucket
      type: string
    - name: DATABASE_USERNAME
      description: AMS DB username
      type: string
    - name: ABOVELENDING_DATABASE_USERNAME
      description: Above Lending DB username
      type: string
    - name: ABOVELENDING_DATABASE_PASSWORD
      description: Above Lending DB password
      type: string
    - name: SIDEKIQ_DASHBOARD_PASSWORD
      description: Sidekiq Dashboard Password
      type: string
    - name: RAILS_ENV
      description: Rails Environment
      type: string
    - name: SECRET_KEY_BASE
      description: Rails Encryption Key
      type: string
    - name: ELIGIBILITY_FILE_BACKUP_S3_BUCKET_NAME
      description: Name of Eligibility File S3 Bucket
      type: string
    - name: ELIGIBILITY_FILE_DEPTH
      description: The maximum number of records to be imported from the eligibility file.
      type: integer
    - name: OFFER_EXPIRATION_DAYS
      description: Amount of days for the offer to be available.
      type: integer
    - name: JWT_DOCUSIGN_EXT_APP_ID
      description: ID of the record in the 'external_apps' table to be used when generating JWTs for the DocuSign webhook URLs.
      type: string
    - name: JWT_PRIVATE_KEY_PATH
      description: Path to the RSA Private Key file to be used when generating JWTs for use by this application.
      type: string
    - name: JWT_PUBLIC_KEY_PATH
      description: Path to the RSA Public Key file to be used when verifying JWT generated by this application.
      type: string
    - name: LANDER_BASE_URL
      description: Homepage URL for the Lander application for this environment.
      type: string
    - name: LOG_LEVEL
      description: The minimum level to log.  One of DEBUG, INFO, WARN, ERROR, FATAL.
      type: string
    - name: REDIS_PREFIX
      description: AMS-specific Redis Key Prefix for Application Cache
      type: string
    - name: REDIS_URI
      description: AMS-specific Redis URI for Application Cache
      type: string
    - name: REDIS_URL
      description: AMS-specific Redis URL for Sidekiq
      type: string
    - name: CONTRACT_DOCUMENTS_BACKUP_S3_BUCKET_NAME
      description: Name of S3 bucket used for uploads of files by service layer
      type: string
    - name: CONTRACT_DOCUMENTS_SECONDARY_BACKUP_S3_BUCKET_NAME
      description: Name of S3 bucket used for uploads of files generated when in secondary mode
      type: string
    - name: TALKDESK_AUTH_BASE_URL
      description: Base URL for getting an oauth token from Talkdesk
      type: string
    - name: TALKDESK_BASE_URL
      description: Base URL for Talkdesk API
      type: string
    - name: TALKDESK_CLIENT_ID
      description: Talkdesk client id
      type: string
    - name: TALKDESK_SECRET
      description: Talkdesk client secret
      type: string
    - name: TALKDESK_DROPOFF_LIST_MAP
      description: A JSON string containing a map from service entity to the Talkdesk record list id for applications that dropped off
      type: string
    - name: TALKDESK_POST_OFFER_DROPOFF_LIST_MAP
      description: A JSON string containing a map from service entity to the Talkdesk record list id for applications that dropped off after offers were made
      type: string
    - name: TALKDESK_APPROVED_DROPOFF_LIST_MAP
      description: A JSON string containing a map from service entity to the Talkdesk record list id for applications that dropped off at approval
      type: string
    - name: SNOWFLAKE_BASE_URL
      description: Base URL for Snowflake SQL REST API
      type: string
    - name: SNOWFLAKE_TIMEOUT
      description: Timeout for calls to Snowflake (in seconds)
      type: integer
    - name: SNOWFLAKE_ACCOUNT
      description: Snowflake account for API calls
      type: string
    - name: SNOWFLAKE_USER
      description: Snowflake user for API calls
      type: string
    - name: SNOWFLAKE_DATABASE
      description: Snowflake database for API calls
      type: string
    - name: SNOWFLAKE_SCHEMA
      description: Snowflake schema for API calls
      type: string
    - name: SNOWFLAKE_WAREHOUSE
      description: Snowflake warehouse for API calls
      type: string
    - name: SNOWFLAKE_ROLE
      description: Snowflake role for API calls
      type: string
    - name: SNOWFLAKE_PRIVATE_KEY
      description: Base64-encoded private key PEM for Snowflake JWT tokens
      type: string
    - name: SOCURE_BASE_URL
      description: Base URL for Socure API
      type: string
    - name: SOCURE_API_KEY
      description: Socure API Key
      type: string
    - name: OCROLUS_AUTH_BASE_URL
      description: Base URL for Ocrolus API authentication
      type: string
    - name: OCROLUS_BASE_URL
      description: Base URL for Ocrolus API
      type: string
    - name: OCROLUS_CLIENT_ID
      description: Client ID used to obtain an authentication token for Ocrolus API calls
      type: string
    - name: OCROLUS_SECRET
      description: Secret used to obtain an authentication token for Ocrolus API calls
      type: string
    - name: OCROLUS_WEBHOOK_USERNAME
      description: The username we expect to be used in the basic authorization attached to any Ocrolus webhook requests
      type: string
    - name: OCROLUS_WEBHOOK_PASSWORD
      description: The password we expect to be used in the basic authorization attached to any Ocrolus webhook requests
      type: string
    - name: POSTGRID_API_KEY
      description: API key to be used to authenticate with the PostGrid API
    - name: DEVISE_SECRET_KEY
      description: Key used by the Devise gem to encrypt password reset tokens and other tokens for user account management.
    - name: DASH_EXTERNAL_BASE_URL
      description: Dash external url (not to be confused with dash /api/ or any other dash internal url for service mesh)
vendor:
  - name: AURORA
    path: '/$ENV/AURORA_CONFIG'
    source: AWS Secrets Manager
    env_vars:
      - name: AURORA_HOST
        description: Aurora database host
        type: string
      - name: AURORA_PORT
        description: Aurora database port
        type: string
  - name: BEYOND_FINANCE
    path: '/$ENV/BEYOND_FINANCE_CONFIG'
    source: AWS Secrets Manager
    env_vars:
      - name: BEYOND_SFTP_HOST
        description: Hostname for the Beyond SFTP server from which the daily eligibility file will be imported
        type: string
      - name: BEYOND_SFTP_USER
        description: Username for connecting to the Beyond SFTP server from which the daily eligibility file will be imported
        type: string
      - name: BEYOND_SFTP_PRIVATE_KEY
        description: Private key for connecting to the Beyond SFTP server from which the daily eligibility file will be imported
        type: string
      - name: BEYOND_LENDING_API_HOST
        description: Beyond Lending API Hostname | where loan status updates are being posted back to Beyond Finance
        type: string
  - name: GDS
    path: '/$ENV/GDS_CONFIG'
    source: AWS Secrets Manager
    env_vars:
      - name: GDS_CLIENT_ID
        description: The client id used by the GDS API wrapper
        type: string
      - name: GDS_SECRET
        description: the client secret used by the GDS API wrapper
        type: string
      - name: GDS_BASE_URL
        description: Base url for api calls made to GDS
        type: string
      - name: GDS_AUTH_BASE_URL
        description: Base url for auth calls made to GDS
        type: string
  - name: SERVICE_LAYER
    path: "/$ENV/SERVICE_LAYER"
    source: AWS Secrets Manager
    env_vars:
      - name: SERVICE_LAYER_BASE_URL
        description: Public URL to send requests from AMS to service-layer Primary (AboveLending DB)
        type: string
    env_vars:
      - name: SERVICE_LAYER_V2_BASE_URL
        description: Public URL to send requests from AMS to service-layer Secondary (Shadow DB)
        type: string
  - name: LOANPRO
    path: "/$ENV/LOANPRO_CONFIG"
    source: AWS Secrets Manager
    env_vars:
      - name: LOANPRO_TOKEN
        description: token required to make authenticated API calls to LoanPro
        type: string
      - name: LOANPRO_INSTANCE_ID
        description: This is the ID of your tenant account
        type: string
      - name: LOANPRO_PCIWALLET_SECRET
        description: PCI Wallet Secret
        type: string
      - name: LOANPRO_PCIWALLET_TOKEN
        description: PCI Wallet Token
        type: string
  - name: SENDGRID
    path: "/$ENV/SENDGRID_CONFIG"
    source: AWS Secrets Manager
    env_vars:
    - name: SENDGRID_VALIDATION_API_KEY
      description: Sendgrid Email Validation API Key
      type: string
  - name: GDS
    path: "/$ENV/GDS_CONFIG"
    source: AWS Secrets Manager
    env_vars:
      - name: GDS_CLIENT_ID
        description: GDS Client Id
        type: string
      - name: GDS_SECRET
        description: GDS Secret
        type: string
      - name: GDS_BASE_URL
        description: GDS base URL
        type: string
      - name: GDS_AUTH_BASE_URL
        description: GDS auth URL
        type: string
  - name: DOCUSIGN
    path: '/$ENV/DOCUSIGN_CONFIG'
    source: AWS Secrets Manager
    env_vars:
      - name: DOCUSIGN_BASE_URL
        description: The base URL for the DocuSign REST API to be used in this environment.
        type: string
      - name: DOCUSIGN_CRB_ACCOUNT_ID
        description: DocuSign account ID to be used for Cross River Bank (CRB) originated applications
        type: string
      - name: DOCUSIGN_CRB_IMPERSONATED_USER_GUID
        description: DocuSign user ID to be impersonated when submitting requests to DocuSign for CRB applications.
        type: string
      - name: DOCUSIGN_CRB_INTEGRATOR_CLIENT_ID
        description: ID of the DocuSign Integrator Client application setup for requests to DocuSign for CRB applications.
        type: string
      - name: DOCUSIGN_CRB_PEM
        description: A base 64 encoded private key to be used when authenticating with DocuSign as the CRB integrator client.
        type: string
  - name: TRUSTPILOT
    path: '/$ENV/TRUSTPILOT_CONFIG'
    source: AWS Secrets Manager
    env_vars:
      - name: TRUSTPILOT_BUSINESS_UNIT_ID
        description: Business Unit Id
        type: string
      - name: TRUSTPILOT_AUTH_TOKEN
        description: Authentication Token
        type: string
      - name: TRUSTPILOT_IPL_TEMPLATE_ID
        description: In Program Loan, Above Graduation Loan (IPL and AGL both same) Template Id
        type: string
        type: string
      - name: TRUSTPILOT_API_KEY
        description: Api Key
        type: string
      - name: TRUSTPILOT_SENDER_EMAIL
        description: Sender Email
        type: string
      - name: TRUSTPILOT_ACCOUNT_ID
        description: Account Id
        type: string
      - name: TRUSTPILOT_ACCOUNT_PASSWORD
        description: Account Password
        type: string
      - name: TRUSTPILOT_REPLY_TO
        description: Reply To
        type: string
      - name: TRUSTPILOT_REDIRECT_URI
        description: Redirect URL
        type: string
      - name: TRUSTPILOT_LOCALE
        description: Locale
        type: string
      - name: TRUSTPILOT_TOKEN_URL
        description: Token URL
        type: string
      - name: TRUSTPILOT_INVITATION_URL
        description: Invitation URL
        type: string
      - name: TRUSTPILOT_BUSINESS_UNITS_PUBLIC_URL
        description: URL of the TrustPilot public business units API.  Defaults to their public url.
        type: string
  - name: DASH
    path: '/$ENV/DASH_CONFIG'
    source: AWS Secrets Manager
    env_vars:
      - name: DASH_API_BASE_URL
        description: Base URL used for all requests made to the Dash API
        type: string
      - name: DASH_ACCESS_TOKEN
        description: Access token included to authenticate all requests made to the Dash API
        type: string
      - name: UPL_DASH_FUNDING_ENABLED
        description: Feature flag controlling whether the Legacy system or Dash is used to process loan funding operations of UPL loans, such as CRB Onboarding (e.g. true/false)
        type: string
  - name: PLAID_CONFIG
    path: '/$ENV/PLAID_CONFIG'
    source: AWS Secrets Manager
    env_vars:
      - name: PLAID_BASE_URL
        description: Base URL used for all requests made to the Plaid API
        type: string
      - name: PLAID_CLIENT_ID
        description: Client ID value used to authenticate all requests sent to the Plaid API
        type: string
      - name: PLAID_SECRET
        description: Secret value used to authenticate all requests sent to the Plaid API
        type: string
  - name: ARIX
    path: '/$ENV/ARIX_CONFIG'
    source: AWS Secrets Manager
    env_vars:
      - name: ARIX_API_BASE_URL
        description: Base URL used for all requests made to the Arix API
        type: string
      - name: ARIX_AUTH_BASE_URL
        description: Access token included to authenticate all requests made to the Arix API
        type: string
      - name: ARIX_CLIENT_ID
        description: Client Id used in authentication with Arix API
        type: string
      - name: ARIX_CLIENT_SECRET
        description: Client secret used in authentication with Arix API
        type: string
