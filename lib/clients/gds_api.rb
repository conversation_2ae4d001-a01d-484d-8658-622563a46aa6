# frozen_string_literal: true

# GDS API integration client
#
# The client looks for an oauth access token in the cache store.
# If it doesn't find an active access_token, it fetches one using client credentials,
# and writes it to the cache.
#
# This code expects the following environment variables to be defined:
#
#   * GDS_BASE_URL
#   * GDS_AUTH_BASE_URL
#   * GDS_CLIENT_ID
#   * GDS_SECRET
#
# Usage example: (create a new loan app)
#
#   loan_app = Clients::GdsApi::LoanApplication.new({...})
#   borrower = Clients::GdsApi::Borrower.new({...})
#   Clients::GdsApi.new_loan_app(product_type: 'IPL', loan_app:, borrower:)
#
# More documentation at:
# https://abovelending.atlassian.net/wiki/spaces/PROD/pages/294452964/GDS+Data+Dictionary+IPL
#
# rubocop:disable Metrics/ClassLength
module Clients
  class GdsApi
    class Error < Clients::Errors::Faraday; end
    class ResponseError < StandardError; end

    GDS_TO_AMS_TASK_STATUS_MAP = {
      approved: 'approved',
      pending: 'pending',
      submit_document: 'submit',
      in_review: 'review',
      rejected: 'rejected'
    }.stringify_keys.freeze

    AMS_TO_GDS_TASK_STATUS_MAP = GDS_TO_AMS_TASK_STATUS_MAP.invert

    class << self
      delegate :get, :post, :patch, :put, :delete, to: :connection
      delegate :base_url, :auth_base_url, :client_id, :client_secret,
               :access_token_cache_key, :gds_path,
               to: :config, private: true

      def active?
        # to check gds is active: call new loan app with an empty body
        # it's done this way in service-layer
        send_request('newLoanApp')
        true
      rescue StandardError => e
        Rails.logger.error("Clients::GdsApi.active? raised #{e.class} with #{e.message}")
        false
      end

      def new_loan_app(product_type:, loan_app:, borrower:)
        check_type('loan_app', loan_app, LoanApplication)
        check_type('borrower', borrower, Borrower)
        send_request('newLoanApp', product_type:, loan_app: loan_app.attributes, borrower: borrower.attributes)
      end

      def patch_loan_app(request_id:, product_type:, loan_app:, borrower:)
        check_type('loan_app', loan_app, LoanApplication)
        check_type('borrower', borrower, Borrower)
        send_request('patchLoanApp', request_id:, product_type:, loan_app: loan_app.attributes,
                                     borrower: borrower.attributes)
      end

      def get_offers(request_id:, loan:, experiment_cohort:)
        loan_app = {
          app_status: loan.loan_app_status.name
        }
        product_type = loan.product_type
        send_request('getOffers', request_id:, loan_app:, product_type:, credit_test_1: experiment_cohort)
      end

      def upl_apply(application:)
        product_type = 'UPL'
        borrower = {
          first_name: application[:first_name],
          last_name: application[:last_name],
          address_street: application[:address_street],
          city: application[:city],
          state: application[:state_code],
          zip_code: application[:zip_code],
          phone_number: application[:phone_number],
          email: application[:email],
          date_of_birth: application[:date_of_birth],
          income: application[:income],
          monthly_housing_payment: application[:monthly_housing_payment],
          ssn: application[:ssn].presence || '000000000'
        }
        loan_app = {
          amount: application[:amount],
          loan_purpose: application[:loan_purpose]
        }
        send_request('getOffers', product_type:, borrower:, loan_app:)
      end

      # When syncing a declined status (e.g. BACK_END_DECLINED), use decision_reason_number to sync the decline reason
      def sync_status(request_id:, product_type:, status:, decision_reason_number: nil)
        check_type('status', status, String)
        loan_app = { app_status: status == 'BANK_SUBMIT' ? 'PENDING' : status }
        params = { request_id:, product_type:, loan_app:, decision_reason_number: }.compact
        send_request('syncStatus', **params)
      end

      def get_tasks(request_id:, app_status:, product_type:)
        send_request('getTasks', request_id:, product_type:, loan_app: { app_status: })
      end

      def update_task_statuses(request_id:, task_statuses:, product_type:)
        check_array_type('task_statuses', task_statuses, TaskStatus)
        send_request('toDoStatusChanges', request_id:, product_type:, tasks: task_statuses.map(&:attributes))
      end

      def update_document_statuses(request_id:, document_statuses:, product_type:)
        check_array_type('document_statuses', document_statuses, DocumentStatus)
        send_request('toDoStatusChanges', request_id:, product_type:, documents: document_statuses.map(&:attributes))
      end

      def get_verification_details(request_id:)
        send_request('verificationRequirements', request_id:)
      end

      def add_bank(request_id:, bank_details:, loan:)
        check_type('bank_details', bank_details, BankDetails)

        loan_app_status = loan.loan_app_status.name
        loan_app = { app_status: loan_app_status == 'BANK_SUBMIT' ? 'PENDING' : loan_app_status }
        product_type = loan.product_type

        send_request('addBank', request_id:, loan_app:, product_type:, **bank_details.attributes)
      end

      def sync_docs(task_id:)
        send_request('syncDocs', task_id:)
      end

      def submit_documents(request_id:, product_type:, task_id:, documents:,
                           loan_app: Clients::GdsApi::LoanApplication.new)
        check_type('loan_app', loan_app, LoanApplication)
        check_array_type('documents', documents, Document)

        send_request(
          'submitDocument',
          request_id:,
          product_type:,
          loan_app: { app_status: loan_app.app_status },
          task_id:,
          documents: documents.map(&:attributes)
        )
      end

      def save_selection(request_id:, offer_id:, app_status:, product_type:, unified_id:)
        send_request(
          'saveSelection',
          request_id:,
          product_type:,
          offer_id:,
          loan_app: { app_status:, unified_id: }
        )
      end

      def download_file(path)
        file_connection.get(path, nil, headers).body
      end

      def public_headers
        headers.dup
      end

      def retrieve_credit_report(request_id:)
        send_request(
          'borrowerReports',
          application_id: request_id,
          report_type: 'informative_soft_pull'
        )
      end

      def retrieve_borrower_reports(request_id:)
        response = send_request(
          'borrowerReports',
          application_id: request_id
        )

        process_borrower_reports_response!(response)
      end

      private

      def send_request(call_type, params = {}, attempt = 1)
        Rails.logger.info("Client::GdsApi#send_request for #{call_type} (attempt #{attempt}):", params:)
        response = post(gds_path, { call_type:, **params }.as_json)
        response_body = ensure_json_body(response.body)
        Rails.logger.info("Client::GdsApi#send_request response for #{call_type} is a #{response_body.class}",
                          params:, request_response_body: loggable_response_body(call_type, response_body))

        # GDS can include an error_message key in the response. Sometimes this indicates an actual error,
        # even on a success status code. But it can also be populated when the request actually succeeded.
        # We log it for informational purposes, but it isn't reliable as in indicator of the outcome.
        log_error_message("#{call_type} - #{response_body['error_message']}") if response_body.key?('error_message')

        response_body
      rescue Faraday::ServerError, Faraday::ConnectionFailed => e
        retry_request(call_type, params, attempt, e)
      end

      def log_error_message(message)
        error = ResponseError.new(message)
        error.set_backtrace(caller) # Set the backtrace so datadog error tracking picks it up
        ExceptionLogger.error(error)
      end

      def retry_request(call_type, params, attempt, error)
        raise Error.new(error, error.response) if attempt > 2

        send_request(call_type, params, attempt + 1)
      end

      def check_type(name, subject, klass)
        error = "#{name} must be an instance of #{klass}"
        raise ArgumentError, error unless subject.is_a?(klass)
      end

      def check_array_type(name, array, klass)
        error = "#{name} must be an array containing instances of #{klass}"
        raise ArgumentError, error unless array.is_a?(Array) && array.all? do |d|
          d.is_a?(klass)
        end
      end

      # used for normal authenticated requests
      def connection
        Faraday.new(url: base_url, headers:) do |f|
          FaradayHelper.middleware(f)
        end
      end

      def ensure_json_body(response_body)
        # NOTE: Although GDS responds with a json-encoded string, it does not
        #       always include the content-type header that would allow our
        #       Faraday middleware to automatically parse the body into json.
        return JSON.parse(response_body) if response_body.is_a?(String)

        response_body
      end

      def file_connection
        Faraday.new(url: base_url, headers:) do |f|
          FaradayHelper.middleware(f, json: false)
        end
      end

      # headers for normal authenticated requests. lowercase to match SL third party request
      def headers
        { 'Authorization' => "Bearer #{access_token}" }
      end

      # fetch or retrieve an oauth access token
      def access_token
        # GDS tokens expire after one hour. To give us some buffer, we cache for 55 minutes.
        Rails.cache.fetch(access_token_cache_key, expires_in: 55.minutes) do
          retrieve_access_token
        end
      end

      # fetch an access token using client credentials flow
      def retrieve_access_token
        response = auth_connection.post('oauth2/token', URI.encode_www_form({ grant_type: 'client_credentials' }))
        response.body['access_token']
      end

      # used exclusively for the oauth token request using client credentials over basic authentication
      def auth_connection
        Faraday.new(url: auth_base_url, headers: auth_headers) do |f|
          f.response :json # decode response bodies as JSON
          FaradayHelper.middleware(f, json: false)
        end
      end

      # headers for the oauth token request
      def auth_headers
        {
          'Authorization' => "Basic #{auth_details}",
          'Content-Type' => 'application/x-www-form-urlencoded'
        }
      end

      def auth_details
        Base64.strict_encode64("#{client_id}:#{client_secret}")
      end

      def config
        @config ||= Rails.application.config_for(:gds_api)
      end

      def process_borrower_reports_response!(response)
        raise ResponseError, 'No reports returned.' if response['reports'].blank?

        response['reports'].collect do |gds_report|
          {
            generated_at: parse_date(gds_report['generated_at']),
            raw_report: gds_report['raw_report'],
            type: gds_report['report_type']
          }
        end
      end

      def parse_date(date_string)
        return nil if date_string.blank?

        # Timestamps are expected to be in ISO-8601 format.
        DateTime.parse(date_string)
      rescue Date::Error => e
        Rails.logger.error('GdsApi - Failed to parse date in response payload', date_string:,
                                                                                error: e.message)
        nil
      end

      # Filters out sensitive data from response bodies that isn't caught by the usual log filtering (like
      # the raw XML reports in borrowerReports responses)
      def loggable_response_body(call_type, response_body)
        return response_body unless call_type == 'borrowerReports'
        return response_body unless response_body.is_a?(Hash) && response_body.key?('reports')

        response_body.except('reports').tap do |loggable_body|
          loggable_body['reports'] = response_body['reports'].map do |report|
            report.except('raw_report').tap do |loggable_report|
              loggable_report['raw_report'] = '[REDACTED]'
            end
          end
        end
      end
    end
  end
end
# rubocop:enable Metrics/ClassLength
