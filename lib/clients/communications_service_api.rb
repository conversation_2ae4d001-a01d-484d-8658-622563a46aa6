# frozen_string_literal: true

module Clients
  class CommunicationsServiceApi
    # These keys must match exactly the keys assigned to template records within the Communications Service.
    TEMPLATE_KEYS = [
      ACH_AUTHORIZATION_RECURRING_PAYMENT_TEMPLATE = 'ach_authorization_recurring_payment',
      ACH_AUTHORIZATION_SINGLE_PAYMENT_TEMPLATE = 'ach_authorization_single_payment',
      ANNUAL_PRIVACY_NOTICE_TEMPLATE = 'annual_privacy_notice',
      BBB_CONSUMER_REVIEW_TEMPLATE = 'bbb_consumer_review',
      CHARGE_OFF_NOTICE_TEMPLATE = 'charge_off_notice',
      CONTACT_SETUP_TEMPLATE = 'setup_above_contact_request',
      CONTRACT_APPROVAL_TEMPLATE = 'contract_approval_message',
      DEBT_VALIDATION_TEMPLATE = 'debt_validation',
      GOOD_BYE_LETTER_QUANTUM = 'good_bye_letter_quantum',
      GOOD_BYE_LETTER_TITAN = 'good_bye_letter_titan',
      GOOD_BYE_LETTER_VELOCITY = 'good_bye_letter_velocity',
      INFO_AND_DISCLOSURE_TEMPLATE = 'information_and_disclosure',
      IPL_OFFERS_TEMPLATE = 'ipl_offers',
      LOAN_APPROVED_TEMPLATE = 'loan_approved',
      NOTICE_OF_ADVERSE_ACTION_TEMPLATE = 'notice_of_adverse_action',
      NOTICE_OF_DEFAULT_KS_TEMPLATE = 'notice_of_default_ks',
      NOTICE_OF_DEFAULT_MO_TEMPLATE = 'notice_of_default_mo',
      NOTICE_OF_DEFAULT_WI_TEMPLATE = 'notice_of_default_wi',
      NOTICE_OF_INCOMPLETE_APPLICATION_TEMPLATE = 'notice_of_incomplete_application',
      OFFER_EXPIRATION_FOMO_TEMPLATE = 'offer_expiration_retargeting_fomo',
      OFFER_EXPIRATION_NO_PREPAYMENT_TEMPLATE = 'offer_expiration_retargeting_no_prepayment',
      OFFER_EXPIRATION_SOCIAL_PROOF_TEMPLATE = 'offer_expiration_retargeting_social_proof',
      POST_OFFER_DROPOFF_TEMPLATE = 'post_offer_dropoff',
      PRE_OFFER_DROPOFF_TEMPLATE = 'pre_offer_dropoff',
      RESET_PASSWORD_INSTRUCTIONS_TEMPLATE_KEY = 'identity_reset_password_instructions',
      STATEMENT_OF_RIGHTS_DC_TEMPLATE = 'statement_of_rights_dc',
      UPL_LOAN_APPROVED_TEMPLATE = 'upl_loan_approved',
      UPL_OFFER_TEMPLATE = 'upl_offer',
      UPL_ONBOARDING_TEMPLATE = 'upl_onboarding',
      WELCOME_TEMPLATE_KEY = 'identity_welcome',
      WELCOME_AND_SET_PASSWORD_IPL_TEMPLATE_KEY = 'identity_welcome_and_set_password_ipl',
      WELCOME_AND_SET_PASSWORD_NON_IPL_TEMPLATE_KEY = 'identity_welcome_and_set_password_non_ipl',
      WELCOME_AND_SET_PASSWORD_WEB_TEMPLATE_KEY = 'identity_welcome_and_set_password_web',
      STAMPED_LOAN_AGREEMENT_KEY = 'stamped_loan_agreement',
      CIP_PAYMENT_SCHEDULED = 'payment_scheduled'
    ].freeze

    DELIVERY_METHODS = [
      EMAIL_DELIVERY_METHOD = 'EMAIL',
      SMS_DELIVERY_METHOD = 'SMS'
    ].freeze

    class Error < Clients::Errors::Faraday; end

    class << self
      SOURCE_ATTRIBUTION_KEY = 'AMS'

      def fetch_messages(filters:, page: 1, per_page: nil)
        conn.get('api/messages', { filter: filters, page:, per_page: }).body
      rescue Faraday::Error => e
        raise Error.new(e, e.response)
      end

      def send_message!(options = {})
        recipient = options[:recipient]
        template_key = options[:template_key]
        inputs = options[:inputs] || {}
        attribution = options[:attribution] || []
        delivery_method = options[:delivery_method] || 'EMAIL'

        attribution << { id: SOURCE_ATTRIBUTION_KEY, type: 'SOURCE' }

        response = conn.post(
          'api/messages',
          {
            recipient:,
            template_key:,
            inputs:,
            attribution:,
            delivery_method:
          }
        )

        response.body
      rescue Faraday::Error => e
        raise Error.new(e, e.response)
      end

      def sendgrid_webhook!(webhook_payload, webhook_headers)
        response = proxy_conn.post(
          'api/sendgrid_webhooks',
          webhook_payload.to_json,
          webhook_headers.merge(content_type: 'application/json')
        )

        {
          status: response.status,
          body: response.body
        }
      rescue Faraday::Error => e
        raise Error.new(e, e.response)
      end

      private

      def url
        Rails.application.config_for(:communications_service).base_url
      end

      def conn
        Faraday.new(url:) do |f|
          FaradayHelper.middleware(f)
        end
      end

      def proxy_conn
        # For proxied requests, we want to return the error response payloads and statuses rather
        # than raising a Ruby error in these cases.
        Faraday.new(url:) do |f|
          FaradayHelper.middleware(f, raise_error: false)
        end
      end
    end
  end
end
