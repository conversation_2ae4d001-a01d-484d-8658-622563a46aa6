# frozen_string_literal: true

# A client facade for interacting with the [DocuSign REST API](https://developers.docusign.com/docs/esign-rest-api/reference/)
module Clients
  class DocusignApi # rubocop:disable Metrics/ClassLength
    AUDIT_EVENT_FIELD_NAME_ACTION = 'Action'
    AUDIT_EVENT_FIELD_NAME_CLIENT_IP_ADDRESS = 'ClientIPAddress'
    AUDIT_EVENT_FIELD_VALUE_SIGNED = 'Signed'

    def account_id
      config[:account_id]
    end

    def build_envelope_definition(unified_id, signer, documents, webhook_url)
      envelope = build_envelope(unified_id)
      envelope.event_notification = build_webhook_configuration(webhook_url)
      envelope.recipients = build_recipients(signer)

      # The order of the documents in this array determines the order in which they will be shown to the user.
      envelope.documents = documents.map { |document| build_envelope_document(document) }

      envelope
    end

    def create_envelope(envelope_definition)
      body, status = envelopes_api.create_envelope_with_http_info(account_id, envelope_definition)

      record_response(request_body: envelope_definition, response: { status:, body: })
      body
    rescue StandardError => e
      record_error(request_body: envelope_definition, exception: e)
      raise
    end

    def get_envelope(envelope_id)
      body, status = envelopes_api.get_envelope_with_http_info(account_id, envelope_id)

      record_response(request_body: nil, response: { status:, body: }, meta: { envelope_id: })
      body
    rescue StandardError => e
      record_error(request_body: nil, exception: e, meta: { envelope_id: })
      raise
    end

    def void_envelope(envelope_id)
      envelope = DocuSign_eSign::Envelope.new
      envelope.status = 'voided'
      envelope.voided_reason = 'Contract has expired'

      body, status = envelopes_api.update_with_http_info(account_id, envelope_id, envelope)

      record_response(request_body: envelope, response: { status:, body: }, meta: { envelope_id: })
      body
    rescue StandardError => e
      record_error(request_body: envelope, exception: e, meta: { envelope_id: })
      raise
    end

    def build_recipient_view_request(signer, contract_signing_token)
      view_request = DocuSign_eSign::RecipientViewRequest.new

      # The DocuSign `return_url` represents where we want recipients to
      # go once they are done signing documents.  Contract documents are
      # injected in Lander in an iframe; this causes the contents of
      # that iframe to be redirected to the return url we configure.
      # EX:  https://abovelending.com/sign-completed/:contract_signing_token
      return_url = URI.join(config[:return_url], '/sign-completed/', contract_signing_token || '')
      view_request.return_url = return_url.to_s
      view_request.authentication_method = 'none'

      # Recipient information must match the recipient info
      # we used to create the envelope.
      view_request.email = signer.email
      view_request.user_name = signer.name
      view_request.client_user_id = config[:client_id]

      view_request
    end

    def create_recipient_view(envelope_id, recipient_view_request)
      body, status = envelopes_api.create_recipient_view_with_http_info(account_id, envelope_id, recipient_view_request)

      record_response(request_body: recipient_view_request, response: { status:, body: },
                      meta: { envelope_id: })
      body
    rescue StandardError => e
      record_error(request_body: recipient_view_request, exception: e, meta: { envelope_id: })
      raise
    end

    def get_document(envelope_id:, document_id:)
      body, status = envelopes_api.get_document_with_http_info(account_id, document_id, envelope_id)

      record_response(request_body: nil, response: { status: }, meta: { document_id:, envelope_id: })
      body
    rescue StandardError => e
      record_error(request_body: nil, exception: e, meta: { document_id:, envelope_id: })
      raise
    end

    def get_signed_client_ip_address(envelope_id)
      body, status = envelopes_api.list_audit_events_with_http_info(account_id, envelope_id)
      signed_client_ip_address = extract_signed_client_ip_address(body)

      record_response(request_body: nil, response: { status:, body: }, meta: { envelope_id: })
      signed_client_ip_address
    rescue StandardError => e
      record_error(request_body: nil, exception: e, meta: { envelope_id: })
      raise
    end

    private

    # https://developers.docusign.com/docs/esign-rest-api/how-to/request-signature-in-app-embedded/
    def build_envelope(unified_id)
      envelope_definition = DocuSign_eSign::EnvelopeDefinition.new
      envelope_definition.email_subject = 'Your Above Lending Loan Documents'
      envelope_definition.status = 'sent'

      text_custom_field = DocuSign_eSign::TextCustomField.new
      text_custom_field.field_id = 'unified_id'
      text_custom_field.name = 'unified_id'
      text_custom_field.show = 'true'
      text_custom_field.value = unified_id

      envelope_definition.custom_fields = DocuSign_eSign::CustomFields.new
      envelope_definition.custom_fields.text_custom_fields = [text_custom_field]

      envelope_definition
    end

    def build_recipients(signer)
      recipients = DocuSign_eSign::Recipients.new
      recipients.signers = [build_signer(signer)]
      recipients
    end

    def build_webhook_configuration(webhook_url) # rubocop:disable Metrics/AbcSize
      event_notification = DocuSign_eSign::EventNotification.new
      event_notification.url = webhook_url
      event_notification.logging_enabled = 'true'
      event_notification.require_acknowledgment = 'true'
      event_notification.use_soap_interface = 'false'
      event_notification.include_certificate_with_soap = 'false'
      event_notification.sign_message_with_x509_cert = 'false'
      event_notification.include_documents = 'false'
      event_notification.include_envelope_void_reason = 'true'
      event_notification.include_time_zone = 'true'
      event_notification.include_sender_account_as_custom_field = 'false'
      event_notification.include_document_fields = 'false'
      event_notification.include_certificate_of_completion = 'false'
      event_notification.envelope_events = [{ envelopeEventStatusCode: 'completed' }]
      event_notification.recipient_events = [{ recipientEventStatusCode: 'Completed' }]
      event_notification
    end

    def build_signer(signer_details)
      signer = DocuSign_eSign::Signer.new
      signer.email = signer_details.email
      signer.name = signer_details.name
      signer.client_user_id = config[:client_id]
      signer.recipient_id = signer_details.recipient_id.to_s

      add_signer_tabs(signer, signer_details.include_state_specific_tabs)

      signer
    end

    def add_signer_tabs(signer, include_state_specific_tabs)
      # Create and assign signable fields (e.g. tabs) to the borrower
      signer.tabs = DocuSign_eSign::Tabs.new
      signer.tabs.sign_here_tabs = [signature_tab]
      signer.tabs.date_signed_tabs = [date_signed_tab]

      return unless include_state_specific_tabs

      signer.tabs.sign_here_tabs << wisconsin_signature_tab
      signer.tabs.initial_here_tabs = [nebraska_initials_tab]
    end

    def date_signed_tab
      ila_date_signed_tab = DocuSign_eSign::DateSigned.new
      ila_date_signed_tab.anchor_string = '/docusign_date_signed/'

      ila_date_signed_tab
    end

    def signature_tab
      signature_tab = DocuSign_eSign::SignHere.new
      signature_tab.anchor_string = '/docusign_sign/'
      signature_tab.anchor_units = 'pixels'
      signature_tab.anchor_x_offset = '50'
      signature_tab.anchor_y_offset = '10'

      signature_tab
    end

    def wisconsin_signature_tab
      signature_tab = DocuSign_eSign::SignHere.new
      signature_tab.anchor_string = '/wisconsin_signature/'
      signature_tab.anchor_units = 'pixels'
      signature_tab.anchor_x_offset = '5'
      signature_tab.anchor_y_offset = '5'

      signature_tab
    end

    def nebraska_initials_tab
      signature_tab = DocuSign_eSign::InitialHere.new
      signature_tab.anchor_string = '/nebraska_initials/'
      signature_tab.anchor_units = 'pixels'
      signature_tab.anchor_x_offset = '5'
      signature_tab.anchor_y_offset = '10'

      signature_tab
    end

    def build_envelope_document(document_details)
      document = DocuSign_eSign::Document.new
      document.document_base64 = Base64.encode64(document_details.content)
      document.name = document_details.name
      document.file_extension = 'pdf'
      document.document_id = document_details.id.to_s

      document
    end

    def extract_signed_client_ip_address(audit_events_response)
      signed_audit_events = audit_events_response.audit_events.filter do |audit_event|
        audit_event.event_fields.any? do |field|
          field.name == AUDIT_EVENT_FIELD_NAME_ACTION &&
            field.value == AUDIT_EVENT_FIELD_VALUE_SIGNED
        end
      end
      return nil if signed_audit_events.empty?

      signed_audit_events.first.event_fields.find do |field|
        field.name == AUDIT_EVENT_FIELD_NAME_CLIENT_IP_ADDRESS
      end&.value
    end

    def fetch_jwt_user_token(client)
      token_expires_in = 1.hour

      Rails.cache.fetch(auth_cache_key, expires_in: token_expires_in - 5.minutes) do
        scopes = 'signature impersonation'

        client.request_jwt_user_token(config[:client_id], config[:impersonated_user_guid],
                                      Base64.decode64(config[:rsa_private_key]), token_expires_in, scopes)
      end
    end

    def auth_cache_key
      "#{config[:auth_token_cache_key]}/#{config[:client_id]}/#{config[:impersonated_user_guid]}"
    end

    def api_client
      return @api_client if defined? @api_client

      configuration = DocuSign_eSign::Configuration.new
      configuration.host = config[:base_url]
      configuration.debugging = ENV.fetch('DOCUSIGN_CLIENT_DEBUGGING', false)

      client = DocuSign_eSign::ApiClient.new(configuration)
      client.set_base_path(config[:base_url])

      token = fetch_jwt_user_token(client)
      client.default_headers['Authorization'] = "#{token.token_type} #{token.access_token}"

      @api_client = client
    end

    def record_response(request_body:, response:, meta: {})
      # NOTE:  Pulls calling location from stack
      # https://www.rubydoc.info/stdlib/core/2.0.0/Kernel%3Acaller_locations
      event_name = "docusign_#{caller_locations(1, 1).first.base_label}"

      # NOTE: Response must be in { status: , response: } hash format to be properly recorded
      RecordApiEvent.call(event_name:, request_body:, response:, meta:)
    end

    def record_error(request_body:, exception:, meta: {})
      # NOTE:  Pulls calling location from stack
      # https://www.rubydoc.info/stdlib/core/2.0.0/Kernel%3Acaller_locations
      event_name = "docusign_#{caller_locations(1, 1).first.base_label}"

      if exception.is_a?(DocuSign_eSign::ApiError)
        response_body = begin
          JSON.parse(exception.response_body)
        rescue StandardError
          exception.response_body
        end

        response = { status: exception.code, body: response_body }
      end

      Rails.logger.error("#{self} - #{exception.class}",
                         error_message: response_body.try(:[], 'message') || exception.message)
      Rails.logger.error("#{self} - Unsuccessful response", meta)

      RecordApiEvent.call(event_name:,
                          request_body:,
                          response:,
                          meta: { account_id: }.merge(meta))
    end

    def envelopes_api
      return @envelopes_api if defined? @envelopes_api

      @envelopes_api = DocuSign_eSign::EnvelopesApi.new(api_client)
    end

    def config
      Rails.application.config_for(:docusign_api)
    end
  end
end
