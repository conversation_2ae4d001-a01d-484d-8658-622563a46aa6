# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Contracts::VoidExpiredContractsJob do
  subject(:job) { described_class.new }

  describe '#perform' do
    let(:loan) { create(:loan) }

    before do
      allow(Contracts::VoidExpiredContracts).to receive(:call)
      allow(Rails.logger).to receive(:error)
    end

    it 'calls VoidExpiredContracts.call' do
      job.perform(loan.id)

      expect(Contracts::VoidExpiredContracts).to have_received(:call).with(loan:)
    end

    context 'when the loan has a decline reason' do
      let(:loan) do
        create(:loan, loan_app_status_id: LoanAppStatus.id(:back_end_declined),
                      decision_reason_number: 5)
      end

      it 'calls VoidExpiredContracts.call with the decision_reason_number' do
        job.perform(loan.id)

        expect(Contracts::VoidExpiredContracts).to have_received(:call).with(loan:)
      end
    end

    context 'when an unexpected error occurs' do
      let(:error) { StandardError.new('boom') }

      before do
        allow(Contracts::VoidExpiredContracts).to receive(:call).and_raise(error)
      end

      it 'logs the error and re-raises it' do
        expect { job.perform(loan.id) }.to raise_error(error)

        expect(Rails.logger).to have_received(:error).with(
          'Failed to void expired contracts',
          hash_including(class: Contracts::VoidExpiredContractsJob, loan_id: loan.id, exception: error)
        )
      end
    end
  end
end
