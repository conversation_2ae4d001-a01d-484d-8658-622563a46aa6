# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LeadBlueprint, type: :blueprint do
  let(:lead) { build(:lead) }
  describe 'normal' do
    subject { described_class.render_as_hash(lead) }
    let(:expected_attributes) do
      {
        id: lead.id,
        first_name: lead.first_name,
        last_name: lead.last_name,
        address: lead.address,
        city: lead.city,
        state: lead.state,
        zip_code: lead.zip_code,
        phone_number: lead.phone_number,
        code_used: lead.code_used,
        type: lead.type,
        date_of_birth: lead.date_of_birth&.strftime('%Y-%m-%d'),
        code_status: 'valid',
        service_entity_name: 'Beyond Finance',
        lead_bank_account: {
          holder_name: lead.nu_dse_holder_s_name_c,
          bank_name: lead.nu_dse_bank_name_c,
          account_number: lead.nu_dse_bank_account_number_c&.to_s,
          routing_number: lead.nu_dse_routing_number_c&.to_s,
          account_type: lead.nu_dse_account_type_c
        }
      }
    end

    it 'renders the proper attributes' do
      expect(subject).to eq(expected_attributes)
    end
  end

  describe 'extended' do
    subject { described_class.render_as_hash(lead, view: :extended) }
    let(:expected_attributes) do
      {
        id: lead.id,
        first_name: lead.first_name,
        last_name: lead.last_name,
        address: lead.address,
        city: lead.city,
        state: lead.state,
        zip_code: lead.zip_code,
        phone_number: lead.phone_number,
        code_used: lead.code_used,
        type: lead.type,
        date_of_birth: lead.date_of_birth&.strftime('%Y-%m-%d'),
        email: lead.email,
        cft_account_details: lead.cft_account_details,
        account_number: lead.account_number,
        loan_details: lead.loan_details,
        fico_score: lead.fico_score,
        client_id: lead.client_id,
        program_id: lead.program_id,
        months_since_enrollment: lead.months_since_enrollment,
        tradeline_details: { 'tradeline_details' => lead.tradeline_details },
        payment_details: lead.payment_details,
        code_status: 'valid',
        service_entity_name: 'Beyond Finance',
        lead_bank_account: {
          holder_name: lead.nu_dse_holder_s_name_c,
          bank_name: lead.nu_dse_bank_name_c,
          account_number: lead.nu_dse_bank_account_number_c&.to_s,
          routing_number: lead.nu_dse_routing_number_c&.to_s,
          account_type: lead.nu_dse_account_type_c
        }
      }
    end

    it 'renders the proper attributes' do
      expect(subject).to eq(expected_attributes)
    end

    describe 'when tradeline_details is an array' do
      let(:lead) { build(:lead, tradeline_details: []) }

      it 'wraps it in a hash' do
        expect(subject[:tradeline_details]).to eq({ 'tradeline_details' => [] })
      end
    end

    describe 'when tradeline_details is not an array' do
      let(:lead) { build(:lead, tradeline_details: {}) }

      it 'renders it as is' do
        expect(subject[:tradeline_details]).to eq({})
      end
    end

    describe 'ineligible code' do
      let(:lead) { build(:lead, expiration_date: Time.now - 5.minutes) }

      it 'sets the code_status' do
        expect(subject[:code_status]).to eq('expired')
      end
    end
  end

  let(:lead_with_nil_values) do
    build(:lead,
          nu_dse_holder_s_name_c: nil,
          nu_dse_bank_name_c: nil,
          nu_dse_bank_account_number_c: nil,
          nu_dse_routing_number_c: nil,
          nu_dse_account_type_c: nil)
  end
  describe 'can handle nil lead_bank_account values' do
    subject { described_class.render_as_hash(lead_with_nil_values) }
    let(:expected_attributes) do
      {
        id: lead_with_nil_values.id,
        first_name: lead_with_nil_values.first_name,
        last_name: lead_with_nil_values.last_name,
        address: lead_with_nil_values.address,
        city: lead_with_nil_values.city,
        state: lead_with_nil_values.state,
        zip_code: lead_with_nil_values.zip_code,
        phone_number: lead_with_nil_values.phone_number,
        code_used: lead_with_nil_values.code_used,
        type: lead_with_nil_values.type,
        date_of_birth: lead_with_nil_values.date_of_birth&.strftime('%Y-%m-%d'),
        code_status: 'valid',
        service_entity_name: 'Beyond Finance',
        lead_bank_account: {
          holder_name: nil,
          bank_name: nil,
          account_number: nil,
          routing_number: nil,
          account_type: nil
        }
      }
    end

    it 'renders the proper attributes' do
      expect(subject).to eq(expected_attributes)
    end
  end
end
