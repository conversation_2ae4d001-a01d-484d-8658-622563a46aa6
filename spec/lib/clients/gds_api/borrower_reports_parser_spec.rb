# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Clients::GdsApi::BorrowerReportsParser do
  describe '.from_reports_response' do
    subject(:call) { described_class.from_reports_response(json_response) }

    let(:json_response) do
      {
        'reports' => [
          {
            'generated_at' => '2023-12-01T10:30:00Z',
            'raw_report' => '<report>Valid report content</report>',
            'report_type' => 'credit_report',
            'success' => true,
            'error_message' => ''
          },
          {
            'generated_at' => '2023-12-02T15:45:00Z',
            'raw_report' => '<report>Another valid report</report>',
            'report_type' => 'income_verification',
            'success' => true,
            'error_message' => ''
          }
        ]
      }
    end

    it 'parses the reports response and returns an array of report hashes' do
      parsed_reports = call

      expect(parsed_reports.length).to eq(json_response['reports'].length)
      json_response['reports'].each do |response_report|
        parsed_report = parsed_reports.find { |report| report[:type] == response_report['report_type'] }
        expect(parsed_report).to be_present
        expect(parsed_report[:generated_at]).to be_within(1.second).of(response_report['generated_at'].to_datetime)
        expect(parsed_report[:raw_report]).to eq(response_report['raw_report'])
        expect(parsed_report[:valid]).to eq(true)
        expect(parsed_report[:error_message]).to be_empty
      end
    end

    context 'when the date is malformed' do
      let(:json_response) do
        {
          'reports' => [
            {
              'generated_at' => 'ten minutes ago',
              'raw_report' => '<report>Valid report content</report>',
              'report_type' => 'credit_report'
            },
            {
              'generated_at' => nil,
              'raw_report' => '<report>Another valid report</report>',
              'report_type' => 'income_verification'
            }
          ]
        }
      end

      it 'gracefully handles the malformed date' do
        parsed_reports = call

        expect(parsed_reports.length).to eq(2)
        parsed_reports.each { |report| expect(report[:generated_at]).to be_nil }
      end
    end

    context 'with mixed valid and invalid reports' do
      let(:json_response) do
        {
          'reports' => [
            {
              'generated_at' => '2023-12-01T10:30:00Z',
              'raw_report' => '<report>Valid content</report>',
              'report_type' => 'credit_report',
              'success' => true,
              'error_message' => ''
            },
            {
              'generated_at' => '2023-12-02T15:45:00Z',
              'raw_report' => '<Response><status>ERROR</status></Response>',
              'report_type' => 'socure',
              'success' => false,
              'error_message' => 'Error Response: E0303 - Client Authentication failed'
            },
            {
              'generated_at' => '2023-12-02T15:45:00Z',
              'raw_report' => '<Identify><VerificationResponse>Error</VerificationResponse></Identify>',
              'report_type' => 'giact',
              'success' => false,
              'error_message' => 'Error Response: 400 - Bad Request'
            },
            {
              'generated_at' => '2023-12-03T09:15:00Z',
              'raw_report' => '',
              'report_type' => 'asset_verification',
              'success' => true,
              'error_message' => ''
            }
          ]
        }
      end

      it 'correctly identifies valid and invalid reports' do
        parsed_report = call

        expect(parsed_report.length).to eq(4)
        expect(parsed_report[0][:valid]).to be true
        expect(parsed_report[1][:valid]).to be false # invalid socure report
        expect(parsed_report[1][:error_message]).to eq('Error Response: E0303 - Client Authentication failed')
        expect(parsed_report[2][:valid]).to be false # invalid giact report
        expect(parsed_report[1][:error_message]).to eq('Error Response: E0303 - Client Authentication failed')
        expect(parsed_report[3][:valid]).to be true
      end
    end
  end
end
