# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Ams::Api::Loans::AppFromInquiry, type: :service do
  include_context 'service with authentication'

  subject(:service_object) { described_class.new(loan_inquiry_id:, esign_consent:, password:) }
  let!(:loan_inquiry)      { create(:loan_inquiry) }
  let(:loan_inquiry_id)    { loan_inquiry.id }
  let(:esign_consent)      { true }
  let(:email)              { loan_inquiry.application['email'] }
  let(:password)           { 'Testpass1!' }

  let!(:constent_doc_templates) do
    DocTemplate::UPL_CONSENT_DOCUMENTS.each do |type|
      create(:doc_template, name: type, type:)
    end
  end
  let(:identity_id) { 'user_id' }

  before do
    Current.oauth_token = 'token'
    allow(Users::CreateUser).to receive(:call).and_call_original
    allow(Documents::GeneratePdf).to receive(:call).exactly(4).times
  end

  describe '#call' do
    describe 'password validations' do
      let(:error_label) { 'A Password with at least 8 characters, 1 numeric , 1 lowercase, 1 uppercase and 1 special character is required.' }
      let(:expected_response) do
        {
          error: 'Bad Request', message:, statusCode: 400,
          errors: [{
            context: errors_context,
            message:,
            path: ['password'],
            type: errors_type
          }]
        }
      end

      context 'when inputted password is longer than 20 characters' do
        let(:password) { 'Shopping 7 $ Candy pf' }
        let(:message) { "\"#{error_label}\" length must be less than or equal to 20 characters long" }
        let(:errors_context) { { key: :password, label: error_label, limit: 20, value: 'Filtered123!' } }
        let(:errors_type) { 'string.max' }

        it 'should return a 400 with an error message' do
          service_object.call
          expect(service_object.body).to eq expected_response
          expect(service_object.status).to eq(400)
        end
      end

      context 'when inputted password does not match requirements' do
        let(:password) { 'Shopping1' }
        let(:message) { "\"#{error_label}\" with value \"Filtered123!\" fails to match the required pattern: /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[^\\w\\s]).{8,}$/" }
        let(:errors_context) { { key: :password, label: error_label, regex: {}, value: 'Filtered123!' } }
        let(:errors_type) { 'string.pattern.base' }

        it 'should return a 400 with an error message' do
          service_object.call
          expect(service_object.body).to eq expected_response
          expect(service_object.status).to eq(400)
        end
      end
    end

    context 'when the loan_inquiry_id is not found' do
      let(:loan_inquiry_id) { 'test' }
      let(:message)         { "LoanInquiry with id #{loan_inquiry_id} not found" }

      it 'should return a not found response' do
        service_object.call
        expect(service_object.body).to eq({ error: 'Not Found', message:, statusCode: 404 })
        expect(service_object.status).to eq(404)
      end
    end

    context 'when the loan inquiry is rejected' do
      let!(:loan_inquiry) { create(:loan_inquiry, decline: {}) }
      let(:message) do
        "Cannot create a loan based on Loan Inquiry with id #{loan_inquiry.id} because it was rejected"
      end

      it 'should return a CannotCreateLoanFromRejectedInquiry error for bad request' do
        service_object.call
        expect(service_object.body).to eq({ error: 'Bad Request', message:, statusCode: 400 })
        expect(service_object.status).to eq(400)
      end
    end

    context 'when the esign_consent is not given' do
      let(:esign_consent) { false }
      let(:message) do
        "Cannot create a loan from Loan Inquiry #{loan_inquiry.id} without signing the e-sign consents"
      end

      it 'should return a ESignConsentRequired error for bad request' do
        service_object.call
        expect(service_object.body).to eq({ error: 'Bad Request', message:, statusCode: 400 })
        expect(service_object.status).to eq(400)
      end
    end

    context 'when loan inquiry offer has expired' do
      let!(:loan_inquiry) { create(:loan_inquiry_expired) }
      let(:message) { 'Offer expired' }

      it 'should return an OffersExpired error for bad request' do
        service_object.call
        expect(service_object.body).to eq({ error: 'Bad Request', message:, statusCode: 400 })
        expect(service_object.status).to eq(400)
      end
    end

    context 'when an ongoing or finished loan exists for borrower email' do
      let(:email) { loan_inquiry.application['email'] }
      let!(:existing_loan) { create(:loan, borrower: build(:borrower, email:)) }
      let(:message) { "An ongoing loan with the email #{email} already exists: #{existing_loan.id}" }

      it 'should return an ExistingLoanForEmail error for bad request' do
        service_object.call
        expect(service_object.body).to eq({ error: 'Method Not Allowed', message:, statusCode: 405,
                                            email:, customCode: 'ONGOING_LOAN_FOR_EMAIL', ongoingLoanId: existing_loan.id })
        expect(service_object.status).to eq(405)
      end
    end

    context 'when there is an error saving offer selection in GDS' do
      let(:message) { 'error saving selection' }

      before do
        expect(Clients::GdsApi).to receive(:save_selection).and_return({ 'error_message' => message })
        service_object.call
      end

      it 'should return ThirdPartyNotWorking error' do
        expect(service_object.body).to eq({ error: 'Third Party Not Working', message:, statusCode: 503 })
        expect(service_object.status).to eq(503)
      end
    end

    context 'when there is an error during loan record creation' do
      before do
        allow(Loan).to receive(:create!).and_raise(StandardError, 'error creating loan')
      end

      it 'should return a 500 error' do
        service_object.call

        expect(Users::CreateUser).not_to receive(:call)
        expect(service_object.body).to eq(
          { error: 'Internal Server Error', message: 'An internal server error occurred', statusCode: 500 }
        )
        expect(service_object.status).to eq(500)
      end
    end

    context 'when input email has mixed case' do
      before do
        expect(Clients::GdsApi).to receive(:save_selection).and_return({ request_id: 'request_id' })
        email.capitalize!
      end

      it 'creates borrower and user records with downcased email' do
        downcased_email = email.downcase
        expect(email).not_to eq(downcased_email)

        service_object.call

        borrower = Borrower.find_by(email: downcased_email)
        expect(borrower.email).to eq(downcased_email)

        expect(Users::CreateUser).to have_received(:call).with(hash_including(email: downcased_email))
      end
    end

    context 'when borrower with a similar email address exists' do
      let!(:existing_borrower) { create(:borrower, email:) }
      before { expect(Clients::GdsApi).to receive(:save_selection).and_return({ request_id: 'request_id' }) }

      it 'uses the existing borrower record and does not create a new one', :aggregate_failures do
        service_object.call
        expect(Borrower).not_to receive(:create!)

        loan = Loan.last
        expect(loan.borrower_id).to eq(existing_borrower.id)

        borrower_additional_info = BorrowerAdditionalInfo.last
        expect(borrower_additional_info.borrower_id).to eq(existing_borrower.id)
      end

      it 'returns success' do
        service_object.call
        expect(service_object.status).to eq(201)
        expect(service_object.body).to eq({ loan_id: Loan.last.id })
      end
    end

    context 'when successful' do
      before do
        allow(Clients::GdsApi).to receive(:save_selection).and_return({ request_id: 'request_id' })
      end

      it 'creates a borrower' do
        expect { service_object.call }.to change(Borrower, :count).by(1)

        borrower = Borrower.find_by(email:)
        expect(borrower.first_name).to eq(loan_inquiry.application['first_name'])
        expect(borrower.last_name).to eq(loan_inquiry.application['last_name'])
        expect(borrower.ssn).to eq(loan_inquiry.application['ssn'])
        expect(borrower.status).to eq(Borrower::VERIFIED_STATUS)
      end

      it 'creates a UPL loan' do
        expect { service_object.call }.to change(Loan, :count).by(1)

        loan = Loan.last
        expect(loan.amount).to eq(loan_inquiry.application['amount'])
        expect(loan.anual_income).to eq(loan_inquiry.application['income'])
        expect(loan.borrower).to eq(Borrower.find_by(email:))
        expect(loan.loan_app_status.name).to eq(LoanAppStatus::PENDING_STATUS)
        expect(loan.monthly_housing_payment).to eq(loan_inquiry.application['monthly_housing_payment'])
        expect(loan.originating_party).to eq(loan_inquiry.offers.first['originating_party'])
        expect(loan.product_type).to eq(Loan::UPL_LOAN_PRODUCT_TYPE)
        expect(loan.purpose).to eq(loan_inquiry.application['loan_purpose'])
        expect(loan.request_id).to eq(loan_inquiry.gds_request_id)
        expect(loan.source_type).to eq(Loan::BEYOND_SOURCE_TYPE)
        expect(loan.unified_id).to be_present
      end

      it 'updates the loan inquiry loan' do
        service_object.call
        expect(loan_inquiry.reload.loan).to eq(Loan.last)
      end

      it 'creates a loan detail record' do
        expect { service_object.call }.to change(LoanDetail, :count).by(1)

        loan_detail = LoanDetail.last
        expect(loan_detail.amount_financed).to eq(loan_inquiry.application['amount'])
        expect(loan_detail.loan).to eq(Loan.last)
      end

      it 'creates borrower additional info record' do
        expect { service_object.call }.to change(BorrowerAdditionalInfo, :count).by(1)

        borrower_additional_info = BorrowerAdditionalInfo.last
        expect(borrower_additional_info.address_apt).to be_nil
        expect(borrower_additional_info.address_street).to eq(loan_inquiry.application['address_street'])
        expect(borrower_additional_info.borrower).to eq(Borrower.find_by(email:))
        expect(borrower_additional_info.city).to eq(loan_inquiry.application['city'])
        expect(borrower_additional_info.loan).to eq(Loan.last)
        expect(borrower_additional_info.phone_number).to eq(loan_inquiry.application['phone_number'])
        expect(borrower_additional_info.state).to eq(loan_inquiry.application['state_code'])
        expect(borrower_additional_info.zip_code).to eq(loan_inquiry.application['zip_code'])
      end

      it 'creates loan offer' do
        expect { service_object.call }.to change(Offer, :count).by(1)
        offer_data = loan_inquiry.offers.first

        offer = Offer.last
        expect(offer.amount).to eq(offer_data['amount'])
        expect(offer.amount_financed).to eq(offer_data['amount_financed'])
        expect(offer.apr).to eq(offer_data['apr'])
        expect(offer.expiration_date).to eq(offer_data['expiration_date'])
        expect(offer.external_creation_date).to eq(DateTime.parse(offer_data['offer_creation_date']))
        expect(offer.external_offer_id).to eq(offer_data['offer_id'])
        expect(offer.interest_rate).to eq(offer_data['interest_rate'])
        expect(offer.loan).to eq(Loan.last)
        expect(offer.monthly_payment).to eq(offer_data['payment_amount'])
        expect(offer.originating_party).to eq(offer_data['originating_party'])
        expect(offer.origination_fee).to eq(offer_data['origination_fee_amount'])
        expect(offer.origination_fee_percent).to eq(offer_data['origination_fee_percent'])
        expect(offer.selected).to eq(true)
        expect(offer.term).to eq(offer_data['term'])
        expect(offer.term_frequency).to eq(offer_data['payment_frequency'])
        expect(offer.type).to eq(Offer::TYPES[:regular])
      end

      it 'updates borrower with identity id' do
        service_object.call

        loan_inquiry = LoanInquiry.last
        expect(Users::CreateUser).to have_received(:call).with(
          email:,
          first_name: loan_inquiry.application['first_name'],
          last_name: loan_inquiry.application['last_name'],
          password:,
          send_email: false,
          service_entity_name: nil
        )

        borrower = Borrower.find_by(email:)
        user = User.find_by(email: email)
        expect(borrower.identity_id).to eq(user.id)
      end

      it 'returns success' do
        expect(Clients::GdsApi).to receive(:save_selection).and_return({ request_id: 'request_id' })
        service_object.call
        expect(service_object.status).to eq(201)
        expect(service_object.body).to eq({ loan_id: Loan.last.id })
      end

      it 'has ABOVE_SELECTED status in loan status history' do
        expect { service_object.call }.to change(LoanStatusHistory, :count).by(2)

        above_selected_exists = LoanStatusHistory.where(loan_id: Loan.last.id, new_status: 'UPL_ABOVE_SELECTED').exists?
        expect(above_selected_exists).to be_truthy
      end

      it 'transitions the loan into the PENDING status' do
        service_object.call

        expect(Loan.last.reload.loan_app_status_id).to eq(LoanAppStatus::ID_TO_NAME.index('PENDING'))
      end

      it 'triggers the save offer selection operation in the GDS API' do
        service_object.call
        expect(Clients::GdsApi).to have_received(:save_selection).with(
          request_id: Loan.last.request_id,
          offer_id: Offer.last.external_offer_id,
          app_status: 'PENDING',
          product_type: Loan.last.product_type,
          unified_id: Loan.last.unified_id
        )
      end

      context 'when enable_update_gds_with_email_validation is disabled' do
        before do
          @gds_feature_flag = Flipper.enabled?(:enable_update_gds_with_email_validation)
          Flipper.disable(:enable_update_gds_with_email_validation)

          allow(Clients::GdsApi).to receive(:save_selection).and_return({ request_id: 'request_id' })
          allow(Clients::GdsApi).to receive(:patch_loan_app).and_return({})
        end

        after do
          process = @gds_feature_flag ? :enable : :disable
          Flipper.send(process, :enable_update_gds_with_email_validation)
        end

        it 'does not include is_valid_email when feature is disabled' do
          expect(Sendgrid::EmailValidate).not_to receive(:call)
          expect(Clients::GdsApi).not_to receive(:patch_loan_app)

          service_object.call
        end
      end

      context 'when enable_update_gds_with_email_validation is enabled' do
        let(:email_validate_double) { double(valid?: true, verdict: 'Valid', score: 0.53) }

        before do
          Flipper.enable(:enable_update_gds_with_email_validation)
          allow(Sendgrid::EmailValidate).to receive(:call).and_return(email_validate_double)
          allow(Clients::GdsApi).to receive(:save_selection).and_return({ request_id: 'request_id' })
          allow(Clients::GdsApi).to receive(:patch_loan_app).and_return({})
        end

        after do
          Flipper.disable(:enable_update_gds_with_email_validation)
        end

        it 'includes is_valid_email when feature is enabled' do
          service_object.call

          expect(Clients::GdsApi).to have_received(:patch_loan_app) do |args|
            expect(args[:borrower].is_valid_email).to be_truthy
          end

          expect(Sendgrid::EmailValidate).to have_received(:call).with(email:, valid_when_record_present: false, source: 'UPL Loans')
        end

        context 'when there is an error with updating email validity in GDS' do
          let(:message) { 'error updating loan' }

          before do
            expect(Clients::GdsApi).to receive(:patch_loan_app).and_return({ 'error_message' => message })
            service_object.call
          end

          it 'should return ThirdPartyNotWorking error' do
            expect(service_object.body).to eq({ error: 'Third Party Not Working', message:, statusCode: 503 })
            expect(service_object.status).to eq(503)
          end
        end
      end
    end
  end
end
