# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Ams::Api::Loans::UplTil, type: :service do
  include ActiveSupport::Testing::TimeHelpers

  subject(:service_object) { described_class.new(**params) }

  let(:email) { loan.borrower.email }
  let(:loan_app_status) { create(:loan_app_status, :offered) }
  let(:loan) { create(:upl_loan, product_type: 'IPL', loan_app_status:) }
  let(:bank_account) { create(:bank_account, loan:, borrower: loan.borrower) }
  let(:loan_payment_detail) { create(:loan_payment_detail, loan_id: loan.id) }
  let(:params) { { custom_authorization: true, loan_id: loan.id } }

  before do
    loan_payment_detail
    bank_account
  end

  describe '#call' do
    context 'when the loan has no selected offer' do
      it 'returns a method not allowed error response' do
        service_object.call
        expect(service_object.body).to eq({
                                            error: 'Method Not Allowed',
                                            message: "There is no offer selected for loan #{loan.id}",
                                            statusCode: 405
                                          })
        expect(service_object.status).to eq(405)
      end
    end

    context 'when the loan has a selected offer' do
      let(:loanpro_loan) { build(:loanpro_loan, loan:) }

      before do
        create(:offer, loan:, selected: true)
        # We cannot trigger the creation of this LoanproLoan record before the CreateLoan service is called,
        # otherwise this would short-circut the "find or create" check that the Til service performs.
        allow(Loanpro::CreateLoan).to receive(:call) do |args|
          args[:above_lending_loanpro_loan].update!(
            id: loanpro_loan.id,
            loanpro_loan_id: loanpro_loan.loanpro_loan_id,
            loanpro_raw_response: loanpro_loan.loanpro_raw_response,
            display_id: loanpro_loan.display_id
          )
          args[:above_lending_loanpro_loan]
        end

        allow(Contracts::GenerateDocusignEnvelope).to receive(:call).and_call_original

        allow(Rails.logger).to receive(:info).and_call_original
      end

      context 'when the loan is in an invalid status' do
        it 'returns a method not allowed error response' do
          service_object.call
          expect(service_object.body).to eq({
                                              error: 'Method Not Allowed',
                                              message: "You cannot perform this action, offers got expired or loan's status is incorrect",
                                              statusCode: 405
                                            })
          expect(service_object.status).to eq(405)
        end
      end

      context 'when the loan is in a valid status' do
        let(:loan_app_status) { create(:loan_app_status, :approved) }

        context 'when at least all offers for the loan are expired' do
          before do
            loan.offers.each { |offer| offer.update!(expiration_date: 1.minute.ago) }
            allow(Loans::SyncStatusJob).to receive(:perform_async)
            allow(Loans::DeliverNoticeOfAdverseActionJob).to receive(:perform_async)
          end

          it 'returns a method not allowed error response' do
            service_object.call
            expect(service_object.body).to eq({
                                                error: 'Method Not Allowed',
                                                message: "You cannot perform this action, offers got expired or loan's status is incorrect",
                                                statusCode: 405
                                              })
            expect(service_object.status).to eq(405)
          end

          it 'updates the loan status to EXPIRED and enqueues jobs to sync with GDS and deliver NOAA' do
            service_object.call
            expect(loan.reload.loan_app_status.name).to eq('EXPIRED')
            expect(Loans::SyncStatusJob).to have_received(:perform_async).with(loan.id)
            expect(Loans::DeliverNoticeOfAdverseActionJob).to have_received(:perform_async).with(loan.id)
          end
        end

        context 'when at least one offer is not expired' do
          let(:til_data) { create(:til_history, associated_loanpro_loan: loanpro_loan, loan:).til_data }
          let(:loan_agreement_document) { build(:contract_document, filename: 'installment-loan-agreement') }
          let(:docusign_webhook_id) { SecureRandom.uuid }
          let(:docusign_envelope_id) { SecureRandom.uuid }
          let(:generate_docusign_envelope_mock) { instance_double(Contracts::GenerateDocusignEnvelope, docusign_webhook_id:, docusign_envelope_id:) }

          before do
            allow(Contracts::BuildUplTilData).to receive(:call).and_return(til_data)
            allow(Documents::GenerateUplInstallmentLoanAgreementPdf).to receive(:call).and_return(loan_agreement_document)
            allow(Contracts::GenerateDocusignEnvelope).to receive(:new).and_return(generate_docusign_envelope_mock)
            allow(generate_docusign_envelope_mock).to receive(:call).and_return(generate_docusign_envelope_mock)
            allow(Contracts::VoidExpiredContractsJob).to receive(:perform_async).and_call_original
          end

          it 'returns an existing LoanPro loan record, if one exists that is not expired' do
            travel_to('2024-08-19T08:00:00Z'.to_time)
            create(:til_history, associated_loanpro_loan: loanpro_loan, loan:)

            offer = loan.offers.last
            offer.update(selected: true)
            loanpro_loan.update!(created_at: 13.hours.ago,
                                 loanpro_raw_response: { LoanSetup: { contractDate: '/Date(1724079833)/' } }.to_json,
                                 offer_id: offer.id)

            service_object.call
            expect(Loanpro::CreateLoan).not_to have_received(:call)
          end

          it 'triggers a LoanPro temporary loan to be created, if one exists but it is expired' do
            loanpro_loan.update!(created_at: 25.hours.ago,
                                 loanpro_raw_response: { LoanSetup: { contractDate: '/Date(0)/' } }.to_json)
            service_object.call
            expect(Loanpro::CreateLoan).to have_received(:call).with(loan:, contract_date: Date, above_lending_loanpro_loan: LoanproLoan)
          end

          it 'triggers a LoanPro temporary loan to be created, if the existing record has invalid raw response data' do
            loanpro_loan.update!(created_at: 25.hours.ago, loanpro_raw_response: 'NOT_VALID_JSON')
            service_object.call
            expect(Loanpro::CreateLoan).to have_received(:call).with(loan:, contract_date: Date, above_lending_loanpro_loan: LoanproLoan)
          end

          it 'triggers a LoanPro temporary loan to be created, if the existing record has no contract date' do
            loanpro_loan.update!(created_at: 25.hours.ago, loanpro_raw_response: { LoanSetup: {} }.to_json)
            service_object.call
            expect(Loanpro::CreateLoan).to have_received(:call).with(loan:, contract_date: Date, above_lending_loanpro_loan: LoanproLoan)
          end

          it 'triggers a LoanPro temporary loan to be created, if one does not already exist' do
            service_object.call
            expect(Loanpro::CreateLoan).to have_received(:call).with(loan:, contract_date: Date, above_lending_loanpro_loan: LoanproLoan)
          end

          it 'calculates the proper contract date when it is before noon CT' do
            travel_to('2024-08-15T16:59:00Z'.to_time) # CT is UTC-5 on this date
            service_object.call
            expect(Loanpro::CreateLoan).to have_received(:call).with(loan:, contract_date: '2024-08-15'.to_date, above_lending_loanpro_loan: LoanproLoan)
          end

          it 'calculates the proper contract date when it is after noon CT' do
            travel_to('2024-08-15T17:01:00Z'.to_time) # CT is UTC-5 on this date
            service_object.call
            expect(Loanpro::CreateLoan).to have_received(:call).with(loan:, contract_date: '2024-08-16'.to_date, above_lending_loanpro_loan: LoanproLoan)
          end

          context 'when reusing an existing contract' do
            let!(:til_history) { create(:til_history, associated_loanpro_loan: loanpro_loan, loan:) }

            before do
              travel_to('2024-08-15T08:00:00Z'.to_time)
              loanpro_raw_response = JSON.parse(loanpro_loan.loanpro_raw_response)
              loanpro_raw_response['LoanSetup'].merge!('contractDate' => '/Date(1723748242)/')
              loanpro_loan.update!(loanpro_raw_response: loanpro_raw_response.to_json, offer_id: loan.selected_offer.id)
            end

            it 'does not generate a new Docusign envelope' do
              expect(Contracts::GenerateDocusignEnvelope).not_to receive(:call)
              expect do
                service_object.call
              end.not_to change(LoanproLoan, :count)

              expect(service_object.body).to be_nil
              expect(service_object.status).to eq(200)
            end
          end

          it 'generates a UPL Installment Loan Agreement document' do
            service_object.call
            expect(Documents::GenerateUplInstallmentLoanAgreementPdf).to have_received(:call) do |loan:, til_history:, loanpro_loan_data:|
              expect(loan).to eq(loan)
              expect(til_history).to be_a(TilHistory)
              expect(til_history.loan_id).to eq(loan.id)
              expect(til_history.til_data).to be_present
              expect(loanpro_loan_data).to match(JSON.parse(LoanproLoan.find_by(loan:).loanpro_raw_response))
            end
          end

          it 'generates a DocuSign envelope for this single document' do
            service_object.call
            expect(Contracts::GenerateDocusignEnvelope).to have_received(:new).with(loan:, loan_agreement_document:, supplemental_documents: [])
          end

          it 'records the TilHistory record for the newly created contract' do
            expect { service_object.call }.to change(TilHistory, :count).by(1)
            expect(Contracts::BuildUplTilData).to have_received(:call).with(loan:, loanpro_loan:)
            til_history = TilHistory.where(loan:).order(created_at: :desc).first
            expect(til_history.docusign_webhook_id).to eq(docusign_webhook_id)
            expect(til_history.docusign_envelope_id).to eq(docusign_envelope_id)
            expect(til_history.til_data).to eq(til_data)

            expect(Contracts::VoidExpiredContractsJob).to have_received(:perform_async).with(loan.id)
            expect(Rails.logger).to have_received(:info).with('Ams::Api::Loans::UplTil: Creating Contract', loanpro_loan:, til_history_id: til_history.id)
          end

          context 'when the borrower is from Maryland' do
            let(:supplemental_documents) do
              [
                build(:contract_document, filename: 'credit-services-contract'),
                build(:contract_document, filename: 'notice-of-cancellation'),
                build(:contract_document, filename: 'duplicate-notice-of-cancellation')
              ]
            end

            before do
              create(:borrower_additional_info, borrower: loan.borrower, state: 'MD')
              allow(Documents::GenerateMarylandContractDocumentPdfs).to receive(:call).and_return(supplemental_documents)
            end

            it 'generates the appropriate supplemental contract documents' do
              service_object.call
              expect(Documents::GenerateMarylandContractDocumentPdfs).to have_received(:call) do |loan:, til_history:|
                expect(loan).to eq(loan)
                expect(til_history).to be_a(TilHistory)
                expect(til_history.loan_id).to eq(loan.id)
                expect(til_history.til_data).to be_present
              end
            end

            it 'generates a DocuSign envelope containing the Installment Loan Agreement and supplemental contract documents' do
              service_object.call
              expect(Contracts::GenerateDocusignEnvelope).to have_received(:new).with(loan:, loan_agreement_document:, supplemental_documents:)
            end
          end
        end
      end
    end
  end
end
