# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Ams::Api::Loans::UplApplyForLoan, type: :service do
  include_context 'service with authentication'

  let(:service_object) { described_class.new(**params) }

  let(:amount) { Faker::Number.decimal }
  let(:loan_purpose) { Ams::Api::Loans::UplApplyForLoan::LOAN_PURPOSES.sample }
  let(:first_name) { Faker::Name.first_name }
  let(:last_name) { Faker::Name.last_name }
  let(:address_street) { Faker::Address.street_address }
  let(:city) { Faker::Address.city }
  let(:state_code) { Faker::Address.state_abbr }
  let(:zip_code) { Faker::Address.zip.first(5) }
  let(:phone_number) { Faker::PhoneNumber.phone_number.gsub(/[^0-9]/i, '').first(10) }
  let(:email) { Faker::Internet.email }
  let(:date_of_birth) { Faker::Date.birthday.strftime('%m-%d-%Y') }
  let(:income) { Faker::Number.decimal }
  let(:monthly_housing_payment) { Faker::Number.decimal }
  let(:ssn) { Faker::Number.number(digits: 9).to_s }
  let(:opp_id) { SecureRandom.uuid }

  let(:params) do
    {
      amount:, loan_purpose:, first_name:, last_name:, address_street:, city:, state_code:, zip_code:, phone_number:,
      email:, date_of_birth:, income:, monthly_housing_payment:, ssn:, oppId: opp_id
    }
  end

  before do
    Current.oauth_token = 'token'
    allow(Clients::CommunicationsServiceApi).to receive(:send_message!)
    allow(Upl::DeliverInfoAndDisclosuresEmailJob).to receive(:perform_async)
  end

  describe '#call' do
    context 'when called with invalid params' do
      %i[amount loan_purpose first_name last_name address_street city state_code zip_code phone_number email
         date_of_birth income monthly_housing_payment].each do |param_key|
        it "validates the presence of the #{param_key} values" do
          params[param_key] = nil

          service_object.call

          expect(service_object.status).to eq(400)
          expect(service_object.body[:statusCode]).to eq(400)
          expect(service_object.body[:error]).to eq('Bad Request')
          expect(service_object.body[:message]).to eq("\"#{param_key}\" is required")
        end
      end

      it 'validates the permitted loan purpose values' do
        params[:loan_purpose] = 'INVALID'

        service_object.call

        expect(service_object.status).to eq(400)
        expect(service_object.body[:statusCode]).to eq(400)
        expect(service_object.body[:error]).to eq('Bad Request')
        expect(service_object.body[:message]).to eq('"loan_purpose" is not included in the list')
      end

      it 'validates the permitted state code length' do
        params[:state_code] = 'ABC'

        service_object.call

        expect(service_object.status).to eq(400)
        expect(service_object.body[:statusCode]).to eq(400)
        expect(service_object.body[:error]).to eq('Bad Request')
        expect(service_object.body[:message]).to eq('"state_code" is the wrong length (should be 2 characters)')
      end

      it 'validates the permitted zip code format' do
        params[:zip_code] = '1234'

        service_object.call

        expect(service_object.status).to eq(400)
        expect(service_object.body[:statusCode]).to eq(400)
        expect(service_object.body[:error]).to eq('Bad Request')
        expect(service_object.body[:message]).to eq('"zip_code" is in the wrong format. It should be in the form 12345 or 12345-1234')
      end

      it 'validates the permitted phone number length' do
        params[:phone_number] = '1234'

        service_object.call

        expect(service_object.status).to eq(400)
        expect(service_object.body[:statusCode]).to eq(400)
        expect(service_object.body[:error]).to eq('Bad Request')
        expect(service_object.body[:message]).to eq('"phone_number" is the wrong length (should be 10 characters)')
      end

      it 'validates the permitted email length' do
        params[:email] = "#{'abc' * 100}@example.com"

        service_object.call

        expect(service_object.status).to eq(400)
        expect(service_object.body[:statusCode]).to eq(400)
        expect(service_object.body[:error]).to eq('Bad Request')
        expect(service_object.body[:message]).to eq('"email" is too long (maximum is 254 characters)')
      end

      it 'validates the permitted email format' do
        params[:email] = 'john@<EMAIL>'

        service_object.call

        expect(service_object.status).to eq(400)
        expect(service_object.body[:statusCode]).to eq(400)
        expect(service_object.body[:error]).to eq('Bad Request')
        expect(service_object.body[:message]).to eq('"email" is invalid')
      end

      it 'validates the permitted date of birth format' do
        params[:date_of_birth] = '1980-12-31'

        service_object.call

        expect(service_object.status).to eq(400)
        expect(service_object.body[:statusCode]).to eq(400)
        expect(service_object.body[:error]).to eq('Bad Request')
        expect(service_object.body[:message]).to eq('"date_of_birth" is invalid')
      end

      it 'validates the permitted ssn format' do
        params[:ssn] = '12345'

        service_object.call

        expect(service_object.status).to eq(400)
        expect(service_object.body[:statusCode]).to eq(400)
        expect(service_object.body[:error]).to eq('Bad Request')
        expect(service_object.body[:message]).to eq('"ssn" is invalid')
      end

      it 'rejects requests with an empty string for the ssn' do
        params[:ssn] = ''

        service_object.call

        expect(service_object.status).to eq(400)
        expect(service_object.body[:statusCode]).to eq(400)
        expect(service_object.body[:error]).to eq('Bad Request')
        expect(service_object.body[:message]).to eq('"ssn" is invalid')
      end

      it 'does NOT trigger the disclosures email to be sent to the borrower' do
        params[:loan_purpose] = nil

        service_object.call

        expect(Clients::CommunicationsServiceApi).not_to have_received(:send_message!)
      end
    end

    context 'when called with valid params' do
      let(:gds_offers_response) do
        {
          request_id: Faker::Alphanumeric.alphanumeric(number: 24),
          message: {},
          product_type: 'UPL',
          ssn: Faker::Number.number(digits: 9).to_s,
          offers: [
            {
              amount: Faker::Number.number(digits: 5),
              amount_financed: Faker::Number.decimal,
              apr: Faker::Number.decimal(l_digits: 2),
              interest_rate: Faker::Number.decimal(l_digits: 2),
              offer_creation_date: Time.zone.today.iso8601,
              offer_id: SecureRandom.uuid,
              originating_party: 'CRB',
              origination_fee_amount: Faker::Number.decimal(l_digits: 3),
              origination_fee_percent: 5,
              payment: Faker::Number.decimal(l_digits: 3),
              term: 60
            }
          ]
        }.as_json
      end

      before do
        allow(Clients::GdsApi).to receive(:upl_apply).and_return(gds_offers_response)
        allow(Upl::SendOfferEmail).to receive(:call)
      end

      context 'when oppId is missing' do
        before { params[:oppId] = nil }

        it 'does not add the oppId to the application details' do
          service_object.call
          expect(Clients::GdsApi).to have_received(:upl_apply).with(application: hash_not_including(:oppId))
          expect(LoanInquiry.last.application).to_not have_key('oppId')
        end
      end

      context 'when zip code is of length 9 (with extra identifier)' do
        before { params[:zip_code] = '12345-6789' }

        it 'trims the zip code to the first 5 characters' do
          service_object.call

          expect(service_object.status).to eq(201)
          expect(Clients::GdsApi).to have_received(:upl_apply).with(application: hash_including(zip_code: '12345'))
          expect(LoanInquiry.last.application['zip_code']).to eq('12345')
        end
      end

      it 'triggers the disclosures email to be sent to the borrower' do
        service_object.call

        expect(Upl::DeliverInfoAndDisclosuresEmailJob).to have_received(:perform_async).with(LoanInquiry.last.id)
      end

      it 'triggers the GDS UPL getOffers operation' do
        service_object.call
        expect(Clients::GdsApi).to have_received(:upl_apply).with(application: {
                                                                    amount:, loan_purpose:, first_name:, last_name:, address_street:, city:, state_code:, zip_code:,
                                                                    phone_number:, email:, date_of_birth:, income:, monthly_housing_payment:, ssn:, oppId: opp_id
                                                                  })
      end

      context 'when there is an error saving the LoanInquiry record' do
        let(:gds_response_with_nil_request_id) do
          {
            request_id: nil,
            message: {},
            product_type: 'UPL',
            ssn: Faker::Number.number(digits: 9).to_s,
            offers: [
              {
                amount: Faker::Number.number(digits: 5),
                amount_financed: Faker::Number.decimal,
                apr: Faker::Number.decimal(l_digits: 2),
                interest_rate: Faker::Number.decimal(l_digits: 2),
                offer_creation_date: Time.zone.today.iso8601,
                offer_id: SecureRandom.uuid,
                originating_party: 'CRB',
                origination_fee_amount: Faker::Number.decimal(l_digits: 3),
                origination_fee_percent: 5,
                payment: Faker::Number.decimal(l_digits: 3),
                term: 60
              }
            ]
          }.as_json
        end

        before do
          allow(Clients::GdsApi).to receive(:upl_apply).and_return(gds_response_with_nil_request_id)
          allow(Upl::SendOfferEmail).to receive(:call)
          allow(Rails.logger).to receive(:error).and_return(true)
        end

        it 'rescues and logs the error' do
          service_object.call
          expect(Rails.logger).to have_received(:error).exactly(3).times
          expect(service_object.body[:error]).to include('Unprocessable Entity')
          expect(service_object.status).to eq(422)
        end
      end

      context 'when SSN is not specified' do
        before { params[:ssn] = nil }

        it 'processes the request without error' do
          service_object.call
          expect(Clients::GdsApi).to have_received(:upl_apply).with(application: hash_including(ssn: params[:ssn]))
        end
      end

      context 'when GDS returns multiple offers' do
        before do
          gds_offers_response['offers'] << {
            amount: Faker::Number.number(digits: 5),
            amount_financed: Faker::Number.decimal,
            apr: Faker::Number.decimal(l_digits: 2),
            interest_rate: Faker::Number.decimal(l_digits: 2),
            offer_creation_date: Time.zone.today.iso8601,
            offer_id: SecureRandom.uuid,
            originating_party: 'CRB',
            origination_fee_amount: Faker::Number.decimal(l_digits: 3),
            origination_fee_percent: 5,
            payment: Faker::Number.decimal(l_digits: 3),
            term: 60
          }.as_json
        end

        it 'returns a 400 response' do
          service_object.call
          expect(service_object.status).to eq(400)
          expect(service_object.body[:error]).to eq('Bad Request')
          expect(service_object.body[:message]).to eq("UPL loan with request_id #{gds_offers_response['request_id']} got more than one offer. This is currently unsupported.")
          expect(service_object.body[:statusCode]).to eq(400)
        end

        it 'does NOT create a loan inquiry record' do
          expect { service_object.call }.not_to change(LoanInquiry, :count)
        end
      end

      context 'when GDS returns an offer but no SSN' do
        before { gds_offers_response['ssn'] = nil }

        it 'returns a 400 response' do
          service_object.call
          expect(service_object.status).to eq(400)
          expect(service_object.body[:error]).to eq('Bad Request')
          expect(service_object.body[:message]).to eq('SSN missing in GDS UPL apply response')
          expect(service_object.body[:statusCode]).to eq(400)
        end

        it 'does NOT create a loan inquiry record' do
          expect { service_object.call }.not_to change(LoanInquiry, :count)
        end
      end

      context 'when GDS returns no offers' do
        before do
          gds_offers_response['offers'] = nil
          gds_offers_response['rejection_data'] = {
            credit_score: Faker::Number.number(digits: 3),
            decision_reason_number: 19,
            decline_reason_text: 'Debt to income ratio',
            decline_reasons: ['Debt to income ratio'],
            score_factor: 'Serious delinquency; Proportion of balances to credit limits is too high on bank revolving or other revolving accounts; Number of accounts with delinquency; Length of time accounts have been established'
          }.as_json
          allow(Upl::DeliverNoticeOfAdverseActionJob).to receive(:perform_async)
          allow(Users::CreateUser).to receive(:call).and_call_original
        end

        it 'creates a loan inquiry record' do
          expect { service_object.call }.to change(LoanInquiry, :count).by(1)

          loan_inquiry = LoanInquiry.where('application->>\'email\' = ?', email).order(created_at: :desc).first
          expect(loan_inquiry.gds_request_id).to eq(gds_offers_response['request_id'])
          expect(loan_inquiry.beyond_request_tracking_id).to be_present
          expect(loan_inquiry.application).to eq({
            amount:,
            loan_purpose:,
            first_name:,
            last_name:,
            address_street:,
            city:,
            state_code:,
            zip_code:,
            phone_number:,
            email:,
            date_of_birth:,
            income:,
            monthly_housing_payment:,
            oppId: opp_id,
            ssn: gds_offers_response['ssn']
          }.as_json)
          expect(loan_inquiry.offers).to be_nil
          expect(loan_inquiry.decline).to eq(gds_offers_response['rejection_data'])
        end

        it 'creates a borrower record with status "unverified"' do
          expect { service_object.call }.to change(Borrower, :count).by(1)

          borrower = Borrower.find_by(email:)
          expect(borrower.first_name).to eq(first_name)
          expect(borrower.last_name).to eq(last_name)
          expect(borrower.ssn).to eq(gds_offers_response['ssn'])
          expect(borrower.status).to eq(Borrower::UNVERIFIED_STATUS)
        end

        it 'creates a loan record with nil unified_id' do
          expect { service_object.call }.to change(Loan, :count).by(1)

          loan = Loan.last
          expect(loan.amount).to eq(amount)
          expect(loan.anual_income).to eq(income)
          expect(loan.borrower).to eq(Borrower.find_by(email:))
          expect(loan.loan_app_status.name).to eq(LoanAppStatus::FRONT_END_DECLINED_STATUS)
          expect(loan.monthly_housing_payment).to eq(monthly_housing_payment)
          expect(loan.originating_party).to be_nil
          expect(loan.product_type).to eq(Loan::UPL_LOAN_PRODUCT_TYPE)
          expect(loan.purpose).to eq(loan_purpose)
          expect(loan.request_id).to eq(gds_offers_response['request_id'])
          expect(loan.source_type).to eq(Loan::BEYOND_SOURCE_TYPE)
          expect(loan.unified_id).to be_nil
        end

        it 'creates a borrower additional info record' do
          expect { service_object.call }.to change(BorrowerAdditionalInfo, :count).by(1)

          borrower_additional_info = BorrowerAdditionalInfo.last
          expect(borrower_additional_info.address_apt).to be_nil
          expect(borrower_additional_info.address_street).to eq(address_street)
          expect(borrower_additional_info.borrower).to eq(Borrower.find_by(email:))
          expect(borrower_additional_info.city).to eq(city)
          expect(borrower_additional_info.loan).to eq(Loan.last)
          expect(borrower_additional_info.phone_number).to eq(phone_number)
          expect(borrower_additional_info.state).to eq(state_code)
          expect(borrower_additional_info.zip_code).to eq(zip_code)
        end

        it 'creates a loan detail record' do
          expect { service_object.call }.to change(LoanDetail, :count).by(1)

          loan_detail = LoanDetail.last
          expect(loan_detail.amount_financed).to eq(amount)
          expect(loan_detail.loan).to eq(Loan.last)
        end

        it 'does not create a user' do
          service_object.call

          expect(Users::CreateUser).not_to have_received(:call)
        end

        it 'triggers the NOAA process' do
          service_object.call

          loan_inquiry = LoanInquiry.where('application->>\'email\' = ?', email).order(created_at: :desc).first
          expect(Upl::DeliverNoticeOfAdverseActionJob).to have_received(:perform_async).with(loan_inquiry.id)
        end

        it 'returns a declined response' do
          service_object.call
          expect(service_object.status).to eq(200)
          expect(service_object.body[:request_tracking_id]).to be_present
          expect(service_object.body[:status]).to eq('FRONT_END_DECLINED')
          expect(service_object.body[:oppId]).to eq(opp_id)
          expect(service_object.body[:reason]).to eq('Debt to income ratio')
          expect(service_object.body[:reasons]).to eq(['Debt to income ratio'])
        end
      end

      context 'when GDS returns a single offer' do
        it 'creates a loan inquiry record' do
          expect { service_object.call }.to change(LoanInquiry, :count).by(1)

          loan_inquiry = LoanInquiry.where('application->>\'email\' = ?', email).order(created_at: :desc).first
          expect(loan_inquiry.gds_request_id).to eq(gds_offers_response['request_id'])
          expect(loan_inquiry.beyond_request_tracking_id).to be_present
          expect(loan_inquiry.application).to eq({
            amount:,
            loan_purpose:,
            first_name:,
            last_name:,
            address_street:,
            city:,
            state_code:,
            zip_code:,
            phone_number:,
            email:,
            date_of_birth:,
            income:,
            monthly_housing_payment:,
            oppId: opp_id,
            ssn: gds_offers_response['ssn']
          }.as_json)
          expect(loan_inquiry.offers).to eq([service_object.body[:offer]])
          expect(loan_inquiry.decline).to be_nil
        end

        it 'triggers the UPL welcome email' do
          service_object.call

          loan_inquiry = LoanInquiry.where('application->>\'email\' = ?', email).order(created_at: :desc).first
          base_url = Rails.application.config_for(:general).lander_base_url
          welcome_link = "#{base_url}/create-account/#{loan_inquiry.id}"

          expect(Clients::CommunicationsServiceApi).to have_received(:send_message!).with(
            recipient: email,
            template_key: Clients::CommunicationsServiceApi::UPL_ONBOARDING_TEMPLATE,
            inputs: {
              full_name: "#{first_name} #{last_name}",
              link: welcome_link
            }
          )
        end

        it 'triggers the UPL offer email' do
          service_object.call

          loan_inquiry = LoanInquiry.where('application->>\'email\' = ?', email).order(created_at: :desc).first
          expect(Upl::SendOfferEmail).to have_received(:call).with(loan_inquiry:)
        end

        it 'returns a successful offer response' do
          service_object.call
          expect(service_object.status).to eq(201)
          expect(service_object.body[:request_tracking_id]).to be_present
          expect(service_object.body[:status]).to eq('OFFERED')
          expect(service_object.body[:oppId]).to eq(opp_id)
          expect(service_object.body[:offer]).to eq({
                                                      'amount' => gds_offers_response['offers'].first['amount'],
                                                      'amount_financed' => gds_offers_response['offers'].first['amount_financed'],
                                                      'apr' => gds_offers_response['offers'].first['apr'],
                                                      # Offer expiration timeframe hard coded to draw attention to this dependency if this value is ever changed.
                                                      'expiration_date' => (Time.zone.today + 28.days).iso8601,
                                                      'interest_rate' => gds_offers_response['offers'].first['interest_rate'],
                                                      'offer_creation_date' => gds_offers_response['offers'].first['offer_creation_date'],
                                                      'offer_id' => gds_offers_response['offers'].first['offer_id'],
                                                      'originating_party' => gds_offers_response['offers'].first['originating_party'],
                                                      'origination_fee_amount' => gds_offers_response['offers'].first['origination_fee_amount'],
                                                      'origination_fee_percent' => gds_offers_response['offers'].first['origination_fee_percent'],
                                                      'payment_amount' => gds_offers_response['offers'].first['payment'],
                                                      'term' => gds_offers_response['offers'].first['term'],
                                                      'payment_frequency' => 'monthly'
                                                    })
        end
      end

      context 'when GDS returns an invalidly formatted offer creation date' do
        before { gds_offers_response['offers'].first['offer_creation_date'] = 'INVALID DATE' }

        it 'gracefully handles this in calculating the expiration date' do
          service_object.call
          expect(service_object.status).to eq(201)
          expect(service_object.body[:offer]['offer_creation_date']).to eq(gds_offers_response['offers'].first['offer_creation_date'])
          expect(service_object.body[:offer]['expiration_date']).to be_nil
        end
      end
    end
  end
end
