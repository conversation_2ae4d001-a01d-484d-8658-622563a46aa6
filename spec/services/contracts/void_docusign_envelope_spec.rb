# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Contracts::VoidDocusignEnvelope, type: :service do
  let(:loan) { create(:loan) }
  let!(:loanpro_loan) { create(:loanpro_loan, loan:) }
  let!(:til_history) { create(:til_history, loan:) }

  let(:docusign_account_id) { SecureRandom.uuid }
  let(:docusign_envelope_status) { 'sent' }
  let(:docusign_envelope) { DocuSign_eSign::Envelope.new(status: docusign_envelope_status) }
  let(:docusign_api_mock) do
    instance_double(Clients::DocusignApi, account_id: docusign_account_id, get_envelope: docusign_envelope, void_envelope: true)
  end

  subject { described_class.new(til_history:) }

  before do
    allow(Clients::DocusignApi).to receive(:new).and_return(docusign_api_mock)
  end

  describe '#call' do
    it 'validates the presence of required attributes' do
      expect(subject).to validate_presence_of(:til_history)
    end

    context 'when the til_history is the latest' do
      it 'raises an exception' do
        expect { subject.call }.to raise_error(Contracts::VoidDocusignEnvelope::TilHistoryActiveError)
      end
    end

    context 'when the til_history is voided' do
      let!(:loanpro_loan) { create(:loanpro_loan, loan:, created_at: 10.seconds.ago) }
      let!(:til_history) { create(:til_history, loan:, created_at: 10.seconds.ago, voided_at: Time.current) }
      let!(:loanpro_loan_active) { create(:loanpro_loan, loan:) }
      let!(:til_history_active) { create(:til_history, loan:) }

      it 'raises an exception' do
        expect { subject.call }.to raise_error(Contracts::VoidDocusignEnvelope::TilHistoryVoidedError)
      end
    end

    context 'when the til_history is valid and voidable' do
      let!(:loanpro_loan) { create(:loanpro_loan, loan:, created_at: 10.seconds.ago) }
      let!(:til_history) { create(:til_history, loan:, created_at: 10.seconds.ago) }
      let!(:loanpro_loan_active) { create(:loanpro_loan, loan:) }
      let!(:til_history_active) { create(:til_history, loan:) }

      context 'when the docusign envelope status is invalid' do
        let(:docusign_envelope_status) { 'signed' }

        it 'raises an exception' do
          expect { subject.call }.to raise_error(Contracts::VoidDocusignEnvelope::InvalidEnvelopeStatusError)
        end
      end

      it 'voids the envelope and database entries' do
        subject.call

        expect(docusign_api_mock).to have_received(:void_envelope).with(til_history.docusign_envelope_id)
        expect(loanpro_loan.reload.deleted_at).not_to be_nil
        expect(til_history.voided_at).not_to be_nil
        expect(til_history.deleted_at).not_to be_nil
      end

      context 'when the docusign envelope status is declined' do
        let(:docusign_envelope_status) { 'declined' }

        it 'voids the database entries without calling docusign' do
          subject.call

          expect(docusign_api_mock).not_to have_received(:void_envelope)
          expect(loanpro_loan.reload.deleted_at).not_to be_nil
          expect(til_history.voided_at).not_to be_nil
          expect(til_history.deleted_at).not_to be_nil
        end
      end
    end
  end
end
