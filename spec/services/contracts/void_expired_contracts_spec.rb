# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Contracts::VoidExpiredContracts, type: :service do
  let(:loan) { create(:loan) }
  let!(:loanpro_loan) { create(:loanpro_loan, loan:) }
  subject { described_class.new(loan:) }

  before do
    allow(Contracts::VoidDocusignEnvelope).to receive(:call)
  end

  describe '#call' do
    it 'validates the presence of required attributes' do
      expect(subject).to validate_presence_of(:loan)
    end

    context 'when there is no til_history entry' do
      it 'does not do anything' do
        subject.call

        expect(Contracts::VoidDocusignEnvelope).not_to have_received(:call)
      end
    end

    context 'when there is a single til_history entry' do
      let!(:til_history) { create(:til_history, loan:) }

      it 'does not do anything' do
        subject.call

        expect(Contracts::VoidDocusignEnvelope).not_to have_received(:call)
      end
    end

    context 'when there are multiple til_history entries' do
      let!(:til_history_a) { create(:til_history, loan:, created_at: 20.seconds.ago) }
      let!(:til_history_b) { create(:til_history, loan:, created_at: 10.seconds.ago) }
      let!(:til_history) { create(:til_history, loan:) }

      it 'voids all expired entries' do
        subject.call

        expect(Contracts::VoidDocusignEnvelope).to have_received(:call).with(til_history: til_history_a)
        expect(Contracts::VoidDocusignEnvelope).to have_received(:call).with(til_history: til_history_b)
        expect(Contracts::VoidDocusignEnvelope).not_to have_received(:call).with(til_history: til_history)
      end
    end

    context 'when there are voided til_history entries' do
      let!(:til_history_a) { create(:til_history, loan:, created_at: 20.seconds.ago, voided_at: 15.seconds.ago) }
      let!(:til_history_b) { create(:til_history, loan:, created_at: 10.seconds.ago) }
      let!(:til_history) { create(:til_history, loan:) }

      it 'ignores voided entries' do
        subject.call

        expect(Contracts::VoidDocusignEnvelope).to have_received(:call).with(til_history: til_history_b)
        expect(Contracts::VoidDocusignEnvelope).not_to have_received(:call).with(til_history: til_history_a)
        expect(Contracts::VoidDocusignEnvelope).not_to have_received(:call).with(til_history: til_history)
      end
    end
  end
end
