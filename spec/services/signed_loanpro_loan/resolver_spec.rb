# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SignedLoanproLoan::Resolver do
  let(:loan) { create(:loan) }
  let!(:loanpro_loan) { create(:loanpro_loan, :signed, loan:) }
  let(:loanpro_loan_api) { create(:loanpro_loan, loan:, created_at: 1.day.ago) }

  subject(:result) { described_class.call(loan:) }

  before do
    allow(Servicing::LoadActiveLoanproLoanFromApi).to receive(:call).and_return(loanpro_loan)
  end

  context 'latest_signed' do
    it 'returns the latest_signed loanpro_loan' do
      expect(result.loanpro_loan).to eq(loanpro_loan)
    end

    it 'has meta data from active loan' do
      expected_meta = {
        ams_loanpro_loan_id: loanpro_loan.id,
        api_loanpro_loan_id: loanpro_loan.id,
        is_loanpro_loan_ids_matched: true,
        is_servicing_loanpro_loan_from_api_enabled: false,
        is_servicing_loanpro_loan_self_healing_enabled: false,
        is_self_healing_needed: false
      }

      expect(result.meta).to eq(expected_meta)
    end

    it 'handles error message for LoanproLoan error and records metadata' do
      expect(Servicing::LoadActiveLoanproLoanFromApi).to receive(:call).and_raise(StandardError.new('Boom!'))

      expect(result.meta[:api_loanpro_loan_error]).to eq('Boom!')
    end
  end

  context 'loanpro_api', with_feature_flag: :servicing_loanpro_loan_from_api do
    before do
      allow(Servicing::LoadActiveLoanproLoanFromApi).to receive(:call).and_return(loanpro_loan_api)
    end

    it 'returns the active loanpro loan defined by Loanpro' do
      expect(result.loanpro_loan).to eq(loanpro_loan_api)
    end

    it 'has meta data from the results' do
      expected_meta = {
        ams_loanpro_loan_id: loanpro_loan.id,
        api_loanpro_loan_id: loanpro_loan_api.id,
        is_loanpro_loan_ids_matched: false,
        is_servicing_loanpro_loan_from_api_enabled: true,
        is_servicing_loanpro_loan_self_healing_enabled: false,
        is_self_healing_needed: false
      }

      expect(result.meta).to eq(expected_meta)
    end
  end

  context 'servicing_loanpro_loan_from_api', with_feature_flag: :servicing_loanpro_loan_from_api do
    before do
      allow(Servicing::LoadActiveLoanproLoanFromApi).to receive(:call).and_return(loanpro_loan_api)
    end

    it 'returns the active loanpro loan defined by Loanpro' do
      expect(result.loanpro_loan).to eq(loanpro_loan_api)
    end

    it 'has meta data from the results' do
      expected_meta = {
        ams_loanpro_loan_id: loanpro_loan.id,
        api_loanpro_loan_id: loanpro_loan_api.id,
        is_loanpro_loan_ids_matched: false,
        is_servicing_loanpro_loan_from_api_enabled: true,
        is_servicing_loanpro_loan_self_healing_enabled: false,
        is_self_healing_needed: false
      }

      expect(result.meta).to eq(expected_meta)
    end
  end

  context 'self healing', with_feature_flag:
    %i[servicing_loanpro_loan_from_api servicing_loanpro_loan_self_healing] do
    let(:self_healer_meta) do
      {
        ams_loanpro_loan_id: loanpro_loan_api&.id,
        til_sign_date: Time.now,
        active_loanpro_loans_count: 1
      }
    end

    let(:self_healer_double) do
      double(SignedLoanproLoan::SelfHealer, ams_loanpro_loan: loanpro_loan_api, meta: self_healer_meta)
    end

    before do
      allow(Servicing::LoadActiveLoanproLoanFromApi).to receive(:call).and_return(loanpro_loan_api)
      allow(SignedLoanproLoan::SelfHealer).to receive(:call).and_return(self_healer_double)
    end

    it 'returns the active loanpro loan defined by Loanpro' do
      expect(result.loanpro_loan).to eq(loanpro_loan_api)
    end

    it 'does not run SelfHealer when ams and api records match' do
      expect(Servicing::LoadActiveLoanproLoanFromApi).to receive(:call).and_return(loanpro_loan)
      expect(SignedLoanproLoan::SelfHealer)
        .not_to receive(:call)

      expect(result.loanpro_loan).to eq(loanpro_loan)
    end

    it 'runs the self heal option' do
      expect(SignedLoanproLoan::SelfHealer)
        .to receive(:call).with(loan:)
        .and_return(self_healer_double)

      expect(result.loanpro_loan).to eq(loanpro_loan_api)
    end

    it 'returns meta from healing' do
      expected_meta = {
        api_loanpro_loan_id: loanpro_loan_api.id,
        is_loanpro_loan_ids_matched: true,
        is_servicing_loanpro_loan_from_api_enabled: true,
        is_servicing_loanpro_loan_self_healing_enabled: true,
        is_servicing_loanpro_loan_healed: true,
        is_self_healing_needed: true
      }.merge(self_healer_meta)

      expect(result.meta).to eq(expected_meta)
    end
  end
end
