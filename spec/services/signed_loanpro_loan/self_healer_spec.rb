# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SignedLoanproLoan::SelfHealer do
  let(:service) { described_class }
  let(:loan) { create(:loan, :onboarded) }

  let(:loanpro_loan) { create(:loanpro_loan, loan:) }
  let(:loanpro_loan_id) { loanpro_loan.loanpro_loan_id }

  let(:loanpro_result) do
    {
      'results' => [
        { 'id' => loanpro_loan_id }
      ],
      'summary' => { 'start' => 0, 'pageSize' => 50, 'total' => 1 }
    }
  end

  subject(:result) { service.call(loan:) }

  before do
    allow(Clients::LoanproApi)
      .to receive(:get_active_loanpro_loan_by)
      .and_return(loanpro_result)
  end

  describe '#call' do
    context 'loan is not onboarded' do
      let(:approved_status) { create(:loan_app_status, name: 'APPROVED') }

      before { loan.update!(loan_app_status: approved_status) }

      it 'raises LoanError' do
        expect { result }.to raise_error(described_class::LoanError, /not onboarded/)
      end
    end

    context 'no active LoanPro loan from API' do
      let(:loanpro_result) do
        {
          'results' => [],
          'summary' => { 'start' => 0, 'pageSize' => 50, 'total' => 0 }
        }
      end

      it 'raises LoanProError' do
        expect { result }.to raise_error(SignedLoanproLoan::SelfHealer::LoanProError, /No Active Loanpro Loans found/)
      end
    end

    context 'multiple AMS loanpro_loans with same unified_id' do
      let(:loanpro_result) do
        {
          'results' => [
            { 'id' => loanpro_loan_id },
            { 'id' => 123_456 }
          ],
          'summary' => { 'start' => 0, 'pageSize' => 50, 'total' => 2 }
        }
      end

      it 'raises LoanProError' do
        expect { result }.to raise_error(SignedLoanproLoan::SelfHealer::LoanProError, /Multiple active LoanPro Loans/)
      end
    end

    context 'no AMS record matches LoanPro API loan ID' do
      let(:loanpro_result) do
        {
          'results' => [
            { 'id' => loanpro_loan_id.to_i + 1 }
          ],
          'summary' => { 'start' => 0, 'pageSize' => 50, 'total' => 1 }
        }
      end

      it 'raises AmsLoanproLoanError' do
        expect { result }.to raise_error(SignedLoanproLoan::SelfHealer::AmsLoanproLoanError, 'AMS loanpro_loan record was not found')
      end
    end

    context 'heals records' do
      let!(:undeleted_loanpro_loan) { create(:loanpro_loan, loan:) }
      let!(:loanpro_loan_with_til_signed) { create(:loanpro_loan, loan:, til_sign_date: 2.days.ago) }

      it 'makes a loanpro_loan a signed_contract?' do
        loanpro_loan.update(deleted_at: Time.now, til_sign_date: nil)
        expect(loanpro_loan.signed_contract?).to be_falsey

        result

        expect(loanpro_loan.reload.signed_contract?).to be_truthy
      end

      it 'marks the other records as deleted' do
        result

        expect(loanpro_loan.reload.signed_contract?).to be_truthy
        expect(undeleted_loanpro_loan.reload.deleted_at).to be_present
        expect(loanpro_loan_with_til_signed.reload.deleted_at).to be_present
        expect(loanpro_loan_with_til_signed.til_sign_date).to be_blank
      end

      it 'has an ams_loanpro_loan' do
        ams_loanpro_loan = result.ams_loanpro_loan

        expect(ams_loanpro_loan).to eq(loanpro_loan)
      end

      it 'handles update transactions' do
        allow_any_instance_of(LoanproLoan).to receive(:update!).and_raise(StandardError)

        expect do
          expect { result }.to raise_error(StandardError)
        end.not_to(change do
                     loan.reload.loanpro_loans.pluck(:updated_at)
                   end)
      end

      it 'raises an error when there are no til_sign_dates' do
        loanpro_loan_with_til_signed.update(til_sign_date: nil)

        expect { result }.to raise_error(SignedLoanproLoan::SelfHealer::AmsLoanproLoanError, /No AMS loanpro_loan til_sign_date found for loan/)
      end
    end

    context 'meta' do
      before { loanpro_loan.update(til_sign_date: Time.now) }

      it 'has loanpro loan, til_sign_date, and loanpro count' do
        loanpro_loan.reload
        meta = result.meta

        expected_meta = {
          ams_loanpro_loan_id: loanpro_loan.id,
          til_sign_date: loanpro_loan.til_sign_date,
          active_loanpro_loans_count: 1
        }

        expect(meta).to eq(expected_meta)
      end
    end
  end
end
