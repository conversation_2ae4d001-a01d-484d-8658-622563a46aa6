# Application Management System (AMS)

This is a Rails 7 app.

## Documentation

This README describes the purpose of this repository and how to set up a development environment.

## Prerequisites

This project requires:

- Ruby 3.3.7, preferably managed using [rbenv](https://github.com/rbenv/rbenv)
- Docker
- Redis

## Setup

AboveLending employs Docker for its deployed environments, providing flexibility for development setups. You have two options:

* Run Locally: Use your local machine's environment for development.
* Use Docker: Leverage our Docker-based setup, known as ALP (Above Lending Platform), for a consistent and containerized development experience.

## Recommendation

We recommend using the ALP Docker setup for the following reasons:

* Matches the deployed environment, ensuring compatibility.
* Simplifies dependency management and configuration, for seamless development in lower environments.
* Provides a unified setup across different development teams.

## ALP V2 Setup

ALP V2 simplifies the development environment by consolidating services into a centralized repository, alp-development. This version introduces improved dependency management, streamlined environment variable handling, and new Makefile commands for enhanced usability.

### Prerequisites

Setup alp-development directions to install Docker, Bitwarden CLI, and setup environment variables.  Follow [ALP-Development Prerequisites](https://github.com/Above-Lending/alp-development/tree/main?tab=readme-ov-file#prerequisites).

In alp-development, verify ALP's healthcheck is valid by running `make doctor`.

### Setup Instructions:
1. Setup alp-development by following it's readme:
  [ALP-Development Prerequisites](https://github.com/Above-Lending/alp-development/tree/main?tab=readme-ov-file#prerequisites)
  - Start services (Postgres, redis, minio)
    ```
    make up
    ```
1. Setup AMS
  - In application_management_system run
    ```
    make alp.upgrade
    make alp.setup
    make alp.secrets.pull
    ```

### Usage

ALP is a fancy wrapper on docker, so docker commands can be called directly on the `alp` service.  In addition AMS includes `sidekiq` and `tailwind` services to create a first class development environment.  In addition a Makefile provides the core functions and calling `make` will print help instructions.

#### Suggested Aliases
Add these aliases to your configuration file (~/.zshrc, ~/.bash_profile, ~/.profile) for easier convenience when working inside a docker container.

```
dc='docker compose'
dcr='dc run --rm'
dcrc='dcr alp command'
dcu='dc up'
```

Example usage:
* `dcu alp` starts the *alp* service in each application
* `dcrc bash` starts the *alp* docker container with a bash interface
* `dcrc bundle` starts the *alp* docker container and runs bundle install
* `dcu up -d && docker attach application_management_system-alp-1` This starts all docker compose services and attaches to the ams container for interacting with the debugger

### Makefile:

Makefile contains core commands to our workflow and simplifies the longer commands like the `docker attach application_managementsystem-alp-1` command above.  Calling `make alp.up` will start all services and attach to a running rails server that will allow `binding.pry` debugging.

| Make Command | Description |
| -------- | ------- |
| make alp.bash	| Opens a bash prompt on a running container. |
| make alp.up | [DEPRECATING] Starts all ALP services. |
| make alp.start | Starts all ALP servicess. |
| make alp.stop	| Stops all ALP services. |
| make alp.exec.bash | Opens a bash prompt on a running container. |
| make alp.setup | Sets up the ALP platform. |
| make alp.upgrade | Upgrades to ALP V2. |
| make alp.downgrade | Downgrades to ALP V1. |
| make alp.secrets.pull | Fetches environment files from Bitwarden. |
| make alp.secrets.push	| Pushes environment files to Bitwarden. |

### ALP Secret Management

ALP uses two sets of secrets: **default rails secrets** and **ALP Environment Variables**. ALP Environment Variables are stored securely in Bitwarden. To manage these, use the provided make commands to Pull or Push secrets from/to Bitwarden.

When updating secrets, **ALWAYS** follow these steps:

1. Pull the latest version using make alp.secrets.pull.
1. Modify the relevant .alp/.env.[environment] file as needed.
1. Push the updated secrets back to Bitwarden using make alp.secrets.push.

#### Default Environment

By default, docker commands will use the DEFAULT environment. The terminal prompt will indicate this with:

```bash
ALP [DEFAULT] /rails_terraform_docker #
```

In this setup, you can run Rails commands (except the server) inside the Docker container. For example, run:

```bash
dcrc bash
```

or

```bash
make alp.bash
```

to open a bash session in the DEFAULT environment.

#### Changing ALP Environments

To switch to a different environment (e.g., Development, Sandbox, or Staging), source the `bin/setenv [environment]` script before running any commands. For example:

```bash
source bin/setenv development
```
This will start a new session with the specified environment and update the terminal prompt accordingly:

```bash
Starting a new shell with:
  COMPOSE_FILE=compose.yml:compose.development.yml
  PS1=ALP [SETENV: development] $

Type 'exit' to leave the development environment.

ALP [SETENV: development] $
```
The prompt clearly indicates that you are running `ALP` in the `SETENV` session for `development` environment.

#### Working in the Environment

With the `setenv` environment prepared, you can run any Docker command in that context. For example:

```bash
ALP [SETENV: development] $ dcrc bash
Running command bash
ALP [development] /rails_terraform_docker #
```
The updated prompt confirms:

* You are working in the `ALP` platform.
* The active environment is `development`.
* You are inside the Docker environment (/rails_terraform_docker).

#### ALP Configuration File

The `.alp/.env.configuration` file simplifies controlling how ALP operates. For example, it allows you to test a Sidekiq job from the sandbox environment while using your local Redis instance to test code changes, all while defaulting to GDS and the sandbox database as the environment is normally configured.

Some options include:
* Local Web Hook Handling: Route and test webhooks locally.
* Local Cache Store: Use a local cache store for development purposes.
* Local Sidekiq Redis: Connect Sidekiq to a local Redis instance.
* Disable Sidekiq inline: Prevent Sidekiq jobs from running inline when running.

#### Important Notes
* Stop all services when switching to a different environment
* Be cautious when working in non-development environments (e.g., Staging). Running migrations in Staging is generally not advisable.
* If you make changes to environment variables, restart the session by exiting (exit) and re-running the Docker setup commands.
* By following these guidelines, you can effectively manage and switch between ALP environments for seamless development and testing.

## Local Setup
To setup a new development environment for this application:

1. Install the Ruby version specified in the `Gemfile`.
   - This repo is setup to support the use of the asdf package manager, in which case you can
     install the necessary dependencies using the following command:
     ```
     asdf plugin-add ruby; asdf plugin-add postgres; asdf install
     ```
1. Run application setup:
   ```
   bin/setup
   ```

## Local Development Environment

Once your local environment is setup, you can use the following commands to run a development
environment directly on your machine:

- To run the web server for this application:
  ```
  bin/rails s
  ```
  - Once started the web server will be accessible at http://localhost:3002/
- To run a development console for this application:
  ```
  bin/rails c
  ```

### Sidekiq

To set up credentials locally, run either command below using credentials found in Bitwarden.

```
bundle config set --global gems.contribsys.com/ {BUNDLE_GEMS__CONTRIBSYS__COM}
bundle config set --local gems.contribsys.com/ {BUNDLE_GEMS__CONTRIBSYS__COM}
```

#### Data-less Setup

This approach will create `abovelending` DB without any data in it. It is useful for development and
unit tests.

The most simple way is to run `docker-compose up --build app`. That will spin AMS in a container and
provision all necessary DBs for it. The app will be available at http://localhost:3002/

If you need to run AMS on the host machine:

```
docker-compose up postgres redis
bin/rails db:setup
bin/rails db:migrate
bin/rails s -p 3002 -b 0.0.0.0
```

## Feature flags

We use [flipper](https://github.com/jnunemaker/flipper) +
[flipper-ui](https://www.flippercloud.io/docs/ui) to manage feature flags.

We use `FLIPPER_UI_SECRET` environment variable to protect flipper-ui endpoint from public access.
This way, feature flags management is available at /<FLIPPER_UI_SECRET>/flipper/features for
production/staging/sandbox environments.

`FLIPPER_UI_SECRET` is empty by default for development, and flipper-ui is available at
http://localhost:3002/flipper/features

## Above Lending Platform (ALP) Docker Setup

Running multiple local instances of ALP services, is using
the `alp` docker compose service. To simplify the setup process, this is defined in Application
management Service `bin/alp/setup` script.

### Setup Instructions for ALP

To set up the Application Management System application, follow the steps below:

#### Prerequisites

Before proceeding with the setup, ensure you have the following:

1. GitHub Access Token:
   - Generate a personal access token (classic) with the necessary permissions to load internal gems
     from GitHub's package management service. Refer to the [Github.com private access
     token](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token).
   - Go to Github account, click on your Profile -> Settings
   - Click on `Developer Settings`
   - Click on `Generate new token` button
   - Choose `Generate new token (classic)`
   - Add a Note for the Above Github token
   - Make expiration for 90 days
   - Grant the access token the `write:packages` permission.
   - Save Github Token for the setup step below
1. Docker:
   - Install Docker to run the applications. You can download Docker from the official website:
     https://www.docker.com/get-started.
1. Setup AWS Account
   - Setup AWS Account with DevOps
     - ECR IAM Permissions: Follow [AWSCLI
       Instructions](https://abovelending.atlassian.net/wiki/spaces/DOP/pages/**********/Using+the+AWS+CLI)
       and post in #devops slack channel any issues you are having with ECR
     - AWS Access Keys
       1. Go to https://aws.amazon.com/ and click on `Sign in to the Console` button
       2. Click on your `email address` on the top right of your screen and click `Security
credentials`
       3. Click on `Create access key`
       4. Click on `Command LIne Interface (CLI)`, check I understand the above recommendation
          (bottom of the form), and click next
       5. Skip Set description tag and click `Create Access Key`
       6. Save both the Access Key and Secret Access Key temporarily for the AWS CLI setup step
          below
       7. Then click `done`
     - Ensure MFA is Setup on your account
       1. Scroll to `Multi-factor authentication (MFA)` section
       2. Copy the `Identifier` and save for the CLI step below. Example:
          `arn:aws:iam:********:mfa/googleauth`
1. AWS CLI: More information for AWS CLI setup for Above Devops can be found at [Using the AWS
   CLI](https://abovelending.atlassian.net/wiki/spaces/PROD/pages/**********/Using+the+AWS+CLI)
   - run `brew install awscli`
   - run `aws configure`
     - Enter the following information when prompted `AWS Access Key ID` - The AWS Access Key ID the
       previous AWS Account Setup `AWS Secret Access Key` - The AWS Secret Access Key the previous
       AWS Account Setup `Default region name` - `us-east-1` `Default output format` - `json`
   - Edit ~/.aws/config
     1. Add the follow to your ~/.aws/config file
     ```
     cli_pager = cat
     mfa_serial = [MFA Identifier From MFA Setup on your account]
     duration_seconds = 3600
     ```

#### Setup Instructions

Follow these steps to setup the applications:

1. Add the following to your shell configuration file (~/.zshrc, ~/.bash_profile, or ~/.profile):
   - GitHub token
   ```
   export GITHUB_TOKEN=[github_token from the GitHub Access Token step above]
   export BUNDLE_GITHUB__COM=[github_token from the GitHub Access Token step above]
   ```
   - You will need to set `BUNDLE_GEMS__CONTRIBSYS__COM` as well. You can get the value for it
     from
     [BitWarden](https://vault.bitwarden.com/#/vault?itemId=f526d92a-b285-409b-af68-aff601169bec&cipherId=f526d92a-b285-409b-af68-aff601169bec).
     Then, add it to your shell config as well:
   ```
   export BUNDLE_GEMS__CONTRIBSYS__COM=value-from-bitwarden
   ```
1. Clone the repository: `<NAME_EMAIL>:Above-Lending/application_management_system.git`
1. Create a `.env.alp.override` file. You can get a starting point for this file from [Bitwarden](https://vault.bitwarden.com/#/vault?search=override&itemId=4768eced-823d-46f6-9f47-b052013d5e0c) or ask a friend with a working environment for theirs.
1. Set up the Application Management System:
   - Navigate to the Application Management System directory.
   - Start Docker

Once you have completed these steps, you will have successfully set up the Application Management
System application.

### Reset

As a docker application, we can rerun the setup script to fully restore the application.

1. Rebuild the docker images
   - run the following command: `bin/alp/setup`

### Additonal Scripts

- `bin/alp/reset_docker`: Removes all containers, images, and volumes
- `bin/alp/bundle_install`: Runs bundle on development machine and inside docker container
- `bin/alp/reset_dbs`: Resets all ALP databases
- `bin/alp/create_minio_buckets`: Creates local buckets for local development

### Using ALP docker in each repository

- `docker compose up alp`: start rails server
- `docker compose exec alp bash` executes the `bash` command inside a running Docker Container
- `docker compose run --rm alp command echo "hello"`: Starts a container runs the entrypoint.sh
  command with the `echo "hello"` command and removes the container.
- `docker compose run --rm alp command bundle install`: Runs bundle install
- `docker compose run --rm alp command bash`: Opens a docker container running bash

### Environment Variables

There are two environment variable files as part of the setup of the `ALP` platform. The default
values go into `.env.alp` and custom changes are stored in `.env.alp.override`.

- `.env.alp`: All _default environment_ variables are stored here
- `.env.alp.override`: Custome environment variables are stored here

## Development Rake Tasks

This repository includes a growing collection of rake tasks to streamline local development. All tasks are organized under the ams namespace. To see the available tasks:

```
bin/rake -T ams
```

### Key Tasks

- Reapply Flow

  Reapply for expired applications:

  ```
  bin/rake ams:dev:reapply_flow
  ```

- Reset Loan and Borrower Data

  Reset loan and borrower data in the development environment:

  ```
  bin/rake ams:dev:db_reset
  ```

## Development Helpers

This project includes development-specific extensions and Rake tasks to simulate local webhooks and similar logic. These tools are designed for development and debugging purposes only and are not applicable to lower environments like sandbox or staging.

### Todo's and Todo Docs

Todo Docs needs GDS call to get a list of todo (verification) documents. Use
`rake ams:mock:todos` to create `todos` and `todo_docs`

* Run `rake ams:mock:todos`
* Enter `loan id`
* Returns todo.id

## Local Webhooks For Third Party Vendors

Local webhook simulation can be enabled in the development environment by setting the `ENABLE_LOCAL_WEBHOOKS` environment variable in the .env.alp file:

The logic for this simulation is implemented in config/initializers/development_extensions.rb.  This introduces a differnt way to trigger webhooks rather than using more complicated 3rd party mocking to speed up development.

Currently it supports:

* Offers: When Clients::GdsApi.get_offers is called:
  - it will generate two offers and change the loan status to OFFERED.

## Environment Variables

Application Management System (AMS) **does not** maintain its own environment variables in our
various [deployed environments](#deployment). Instead, Above's DevOps team maintains variables in a
separate infrastructure and links them in the build and deployment of the application. Because of
this, some coordination is required with DevOps when environment variables are modified. The
environment variable schema is maintained in this codebases' `manifest.yaml`, and the communication
of the _values_ of those variables is done via Bitwarden.

When adding a new variable, modifying the value of an existing variable, or removing a variable, we
must:

1. Add the key and a sample value to `.env.example` and `.env.test`
2. When adding or removing a variable: update [manifest.yaml](manifest.yaml) to include/remove the
   variable
3. Update **each** of the Bitwarden notes to include/remove/update the value of the variable in
   question:
   - ADD ME

## Local Validation

When making changes to the application, use the following commands to validate that they are working
as expected and match the coding conventions for this repo:

- Run the automated test suite:
  ```
  bin/rspec
  ```
  - Run a single test:
    ```
    bin/rspec [PATH_TO_TEST_FILE_OR_DIRECTORY]
    ```
  - Run a single scenario within test:
    ```
    bin/rspec [PATH_TO_TEST_FILE_OR_DIRECTORY]:[LINE_NUMBER_OF_TARGET_SCENARIO]
    ```
- Run the Rubocop static analysis checks:
  ```
  bin/rubocop
  ```
  - Automatically correct all "correctable" issues:
    ```
    bin/rubocop -aD
    ```
- Run the Brakeman static analysis checks:
  ```
  bin/brakeman
  ```

## Deployment

ADD ME

### .ci/

The `.ci/` folder is maintained by the devops team and can be safely ignored by folks working on the
application itself.

## Background Jobs

We are using [Sidekiq](https://github.com/sidekiq/sidekiq) to execute and manage background jobs
within this application. A web dashboard is hosted under the `/sidekiq` route and is secured via
basic HTTP authentication. In development environments jobs will automatically be run within the
same process as the web server. In deployed environments (e.g. staging, production, etc) a separate
pod will be used to isolate the execution of background jobs from the web server pod.

### Logging

AMS uses [Semantic Logger](https://logger.rocketjob.io/rails.html) for structured logging. In deployed
environments, JSON logs are written to the container's stdout, which is forwarded to Datadog. Logs can
be searched in the [Datadog Log Explorer](https://app.datadoghq.com/logs?saved-view-id=3494211).

## API Documentation

API docs are generated to the [swagger](http://swagger.io/) specification with the [rswag
gem](https://github.com/rswag/rswag). Rswag DSL is used in request specs to define the output of
documentation, which can be reached at `localhost:3002/api-docs`

example request spec file:

> `spec/requests/ping_request_spec.rb`

Run this command to generate the yml file that is used to display the documentation pages.

`$ rails rswag`

### Generating model files from schema definitions

This application has a script for generating model files automatically from schema files.

The schema files are located [here](https://github.com/Above-Lending/data-architecture-models). They
are using the [open api specification](https://swagger.io/docs/specification/about/).

**🚨 Warning: Running this script will overwrite your existing model files 🚨**

1. On the same level as the root folder of this application, check out the [repository of the
   schemas](https://github.com/Above-Lending/data-architecture-models).
2. Run the command `bin/generate_models`

This script can be run with some command line arguments:

- `--debug`: output debug yaml comments in the generated models and specs.
- `--clean`: clean up old generated models before generating

The script will load any schema definitions (yaml files) into a config hash, and parse that into
models and specs for these models. In the source code, an include list is defined (an array of
identifiers/names). This list decides which models from the yaml files are generated and which
aren't.

Caveat: This script is not a complete implementation of the open api specification. It just
implements what we need for generating from the above-mentioned repository.

### Redis

AMS is configured to use Redis for caching and for sidekiq jobs. To start using Redis for caching
please set the `REDIS_URI` env variable. Sidekiq uses the predefined `REDIS_URL` env variable.

### Authentication with rswag testing DSL

RSwag is configured in `swagger_helper.rb` to know about the application's authentication methods,
but parameters will need to be set in a request spec in order to authenticate properly.

```ruby
# required line in https method block of swagger test setup

security [{ token: [] }]

# request spec file example, ping_request_spec.rb

RSpec.describe 'ping_request', type: :request do
  path '/ping_request' do
    get('index ping') do
      tags 'Ping'
      consumes 'application/json'
      security [{ token: [] }]
      # ...

# Auth header must be set in the response block

let(:Authorization) { "Token #{ENV.fetch('SOME_TOKEN')}" }

# example, ping_request_spec.rb
  response '202', 'successful' do
    let(:Authorization) { "Token #{ENV.fetch('SOME_TOKEN')}" }
    # ...
```

### Production

Deployment of the Production environment is done manually via Github Actions by a small group of Product Owners. Developers should not be pushing to production as outlined by the SDLC. It uses the same "[Manual Deployment Workflow](https://github.com/Above-Lending/dash/actions/workflows/manual-deployment.yaml)" as Sandbox and Staging deployments, but with a few extra steps, as follows:

1. From your local environment run the following command to generate and push a tag to Github
```
 bin/rake tag
```
1. Follow the link from the command or visit the "[Tags](https://github.com/Above-Lending/dash/tags)" section of Github for the repository.
1. Click "Create release from tag" near the top right.
1. Near the release description, click "Generate release notes." This will automatically fill-in the description of the released with a list of commits indicating the changes contained in the release.
1. Next, **mark** "Set as a pre-release" and ensure that "Set as the latest release" is **unmarked**, then hit "Publish release." Announce the release in our team's slack channel with a link back to the github release that was just created.
1. Announce the intention to deploy in our team's slack channel first, with a link to the release.
1. At this point you should coordinate with the Product Owners to ask them to release the proposed changes. The steps below should not be run by engineers but are present for informational purposes.
1. As in Staging and Sandbox, we make use of the "[Manual Deployment Workflow](https://github.com/Above-Lending/dash/actions/workflows/manual-deployment.yaml)":
    1. Near the top-right, open the "Run Workflow" menu.
    1. This opens a drop-down allowing for the selection of a branch or tag, as well as the "Destination Environment."
    1. Select the tag that represents the release, e.g., `2145.11.30.09.45`
    1. Then select the `prod` "Destination Environment."
    1. Click "Run Workflow." This triggers the deployment workflow, which may be monitored by refreshing the page and then selecting the now-running "Manual Deployment Workflow" from the middle table.
1. After deployment is completed, return to the release in github. Update it, **unmarking** "Set as a pre-release", and **marking** "Set as the latest release."
1. Finally, back in Jira, move the cards in the "Accepted" Column that correspond to the release over to "Released."

### Maintenance

To optimize deployments, archive older `db/migrate` files into `db/archive_migrate`. This reduces the size of migrations processed during deployment, speeding up the process.

Move migrations older than 3-4 weeks from `db/migrate` to `db/archive_migrate`.

