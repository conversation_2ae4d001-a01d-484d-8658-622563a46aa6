# frozen_string_literal: true

class RemoveDuplicateIndexOnCrbStatusesId < ActiveRecord::Migration[7.2]
  def up
    # First drop this FK that depends on the redundant constraint and index
    remove_foreign_key :crb_loans, name: :'FK-crb-statuses-crb-loan'

    # Drop the redundant unique constraint and index
    execute <<~SQL
      ALTER TABLE crb_statuses DROP CONSTRAINT IF EXISTS crb_statuses_id_unique;
    SQL
    remove_index :crb_statuses, column: :id, name: :crb_statuses_id_unique, if_exists: true

    # Re-add the FK; now it should get bound to the primary key
    add_foreign_key :crb_loans, :crb_statuses, column: :crb_status_id
  end

  def down
    # Remove the FK pointing to the PK
    remove_foreign_key :crb_loans, column: :crb_status_id

    # Recreate the unique index and constraint
    add_index :crb_statuses, :id, name: :crb_statuses_id_unique, unique: true unless index_exists?(:crb_statuses, :id, name: :crb_statuses_id_unique)
    execute <<~SQL
      ALTER TABLE crb_statuses ADD CONSTRAINT crb_statuses_id_unique UNIQUE USING INDEX crb_statuses_id_unique;
    SQL

    # Recreate the original named foreign key
    add_foreign_key :crb_loans, :crb_statuses,
                    column: :crb_status_id,
                    name: :'FK-crb-statuses-crb-loan'
  end
end
