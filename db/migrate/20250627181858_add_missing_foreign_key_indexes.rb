# frozen_string_literal: true

class AddMissingForeignKeyIndexes < ActiveRecord::Migration[7.2]
  disable_ddl_transaction!

  def up
    add_index :bank_accounts, :loan_id, algorithm: :concurrently, if_not_exists: true
    add_index :borrower_aditional_info, :borrower_id, algorithm: :concurrently, if_not_exists: true
    add_index :borrower_aditional_info, :loan_id, algorithm: :concurrently, if_not_exists: true
    add_index :crb_loans, :loan_id, algorithm: :concurrently, if_not_exists: true
    add_index :docs, :loan_inquiry_id, algorithm: :concurrently, if_not_exists: true
    add_index :loan_credit_data, :borrower_id, algorithm: :concurrently, if_not_exists: true
    add_index :loan_credit_data, :loan_id, algorithm: :concurrently, if_not_exists: true
    add_index :loan_credit_data, :loan_inquiry_id, algorithm: :concurrently, if_not_exists: true
    add_index :loan_details, :loan_id, algorithm: :concurrently, if_not_exists: true
    add_index :loan_inquiries, :loan_id, algorithm: :concurrently, if_not_exists: true
    add_index :loan_offer_reminders, :loan_id, algorithm: :concurrently, if_not_exists: true
    add_index :loan_tradeline_details, :loan_id, algorithm: :concurrently, if_not_exists: true
    add_index :loanpro_loans, :loan_id, algorithm: :concurrently, if_not_exists: true
    add_index :loanpro_loans, :offer_id, algorithm: :concurrently, if_not_exists: true
    add_index :loans, :investor_id, algorithm: :concurrently, if_not_exists: true
    add_index :loans, :loan_app_status_id, algorithm: :concurrently, if_not_exists: true
    add_index :product_type_states, :state_id, algorithm: :concurrently, if_not_exists: true
    add_index :temporary_token, :loan_id, algorithm: :concurrently, if_not_exists: true
    add_index :til_history, :loan_id, algorithm: :concurrently, if_not_exists: true
  end

  def down
    remove_index :bank_accounts, :loan_id if index_exists?(:bank_accounts, :loan_id)
    remove_index :borrower_aditional_info, :borrower_id if index_exists?(:borrower_aditional_info, :borrower_id)
    remove_index :borrower_aditional_info, :loan_id if index_exists?(:borrower_aditional_info, :loan_id)
    remove_index :crb_loans, :loan_id if index_exists?(:crb_loans, :loan_id)
    remove_index :docs, :loan_inquiry_id if index_exists?(:docs, :loan_inquiry_id)
    remove_index :loan_credit_data, :borrower_id if index_exists?(:loan_credit_data, :borrower_id)
    remove_index :loan_credit_data, :loan_id if index_exists?(:loan_credit_data, :loan_id)
    remove_index :loan_credit_data, :loan_inquiry_id if index_exists?(:loan_credit_data, :loan_inquiry_id)
    remove_index :loan_details, :loan_id if index_exists?(:loan_details, :loan_id)
    remove_index :loan_inquiries, :loan_id if index_exists?(:loan_inquiries, :loan_id)
    remove_index :loan_offer_reminders, :loan_id if index_exists?(:loan_offer_reminders, :loan_id)
    remove_index :loan_tradeline_details, :loan_id if index_exists?(:loan_tradeline_details, :loan_id)
    remove_index :loanpro_loans, :loan_id if index_exists?(:loanpro_loans, :loan_id)
    remove_index :loanpro_loans, :offer_id if index_exists?(:loanpro_loans, :offer_id)
    remove_index :loans, :investor_id if index_exists?(:loans, :investor_id)
    remove_index :loans, :loan_app_status_id if index_exists?(:loans, :loan_app_status_id)
    remove_index :product_type_states, :state_id if index_exists?(:product_type_states, :state_id)
    remove_index :temporary_token, :loan_id if index_exists?(:temporary_token, :loan_id)
    remove_index :til_history, :loan_id if index_exists?(:til_history, :loan_id)
  end
end
