class CreateGoodByeLetterTitan < SeedMigration::Migration
    TEMPLATE_PATH = Rails.root.join('db', 'data', 'templates', '20250711124000_good_bye_letter_titan_1.html')
    TEMPLATE_NAME = 'Good Bye Letter Titan'
    TEMPLATE_TYPE = DocTemplate::TYPES[:GOOD_BYE_LETTER_TITAN]
    TEMPLATE_URI = 'https://abovelending.com'
    TEMPLATE_VERSION = 1
  
    def up
      template_content = File.read(TEMPLATE_PATH)
  
      DocTemplate.create!(
        id: SecureRandom.uuid,
        name: TEMPLATE_NAME,
        version: TEMPLATE_VERSION,
        uri: TEMPLATE_URI,
        body: template_content,
        type: TEMPLATE_TYPE,
        doc_content_type: 'html'
      )
    end
  
    def down
      DocTemplate.find_by(type: TEMPLATE_TYPE, version: TEMPLATE_VERSION)&.destroy!
    end
  end
  