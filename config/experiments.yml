# Please reference this document: https://abovelending.atlassian.net/wiki/spaces/SE/pages/2142175250/Run+an+Experiment
# for more detailed information on running experiments.

# Experiments should be specified in the `shared` config
# and overridden in environment-specific configuration blocks as necessary.
# Rails automatically merges the `shared` block into each environment-specific config.

# Below is an example experiment config, using the following convention for its name:
# [YEAR]_[MONTH]_[JIRA-TICKET]_[CONTEXT]

# Each experiment can define a `default` cohort, which will be used when no subject has been assigned
# (e.g., before the experiment was created or for read-only fallbacks via `fetch_cohort_for`).

# 2024_06_CHI_667_Lander_Loan_Years_On_Offer_Select:
#   default: champion # [Optional] returned by `fetch_cohort_for` if subject is unassigned
#   cohorts:
#     champion: 50
#     challenger: 50
shared:
  2025_04_CHI_1753_Credit_Model_1_0:
    default: champion
    cohorts:
      champion: 100
      challenger: 0

test: {}

development: {}

sandbox: {}

staging: {}

production: {}
