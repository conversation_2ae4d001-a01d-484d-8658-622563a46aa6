name: Manual Deployment Workflow
run-name: Manual Deployment to ${{ inputs.DestEnvironment }}-${{ inputs.AppDevName }} by @${{ github.actor }}

on:
  workflow_dispatch:
    inputs:
      DestEnvironment:
        type: choice
        description: Destination Environment
        required: true
        options:
          - production
          - sandbox
          - staging
        default: 'sandbox'
      AppDevName:
        type: choice
        description: Destination Environment
        required: true
        options:
          - ams
          - ams-2
        default: 'ams'

jobs:
  cd-build:
    uses: ./.github/workflows/cd-build.yaml
    secrets: inherit
    with:
      DestEnvironment: ${{ inputs.DestEnvironment }}

  cd-deploy-sandbox-ecs:
    if: inputs.DestEnvironment == 'sandbox' && inputs.AppDevName == 'ams'
    uses: ./.github/workflows/cd-deploy-ams-ecs-sandbox.yaml
    needs: [cd-build]
    secrets: inherit
    with:
      DestEnvironment: ${{ inputs.DestEnvironment }}
      AppDevName: ${{ inputs.AppDevName }}

  cd-deploy-sandbox-2-ecs:
    if: inputs.DestEnvironment == 'sandbox' && inputs.AppDevName == 'ams-2'
    uses: ./.github/workflows/cd-deploy-ams-2-ecs-sandbox.yaml
    needs: [cd-build]
    secrets: inherit
    with:
      DestEnvironment: ${{ inputs.DestEnvironment }}
      AppDevName: ${{ inputs.AppDevName }}

  cd-deploy-staging-ecs:
    if: inputs.DestEnvironment == 'staging' && inputs.AppDevName == 'ams'
    uses: ./.github/workflows/cd-deploy-ams-ecs-staging.yaml
    needs: [cd-build]
    secrets: inherit
    with:
      DestEnvironment: ${{ inputs.DestEnvironment }}
      AppDevName: ${{ inputs.AppDevName }}

  cd-deploy-production-ecs:
    if: inputs.DestEnvironment == 'production' && inputs.AppDevName == 'ams'
    uses: ./.github/workflows/cd-deploy-ams-ecs-production.yaml
    needs: [cd-build]
    secrets: inherit
    with:
      DestEnvironment: ${{ inputs.DestEnvironment }}
      AppDevName: ${{ inputs.AppDevName }}
