name: AMS ECS Deploy - Production

on:
  workflow_call:
    inputs:
      DestEnvironment:
        type: string
        description: Destination Environment
        required: true
      AppDevName:
        type: string
        description: App name with suffix for multi-dev environments
        required: true
        default: ams
    secrets:
      GH_TOKEN:
        required: true
      SLACK_BOT_TOKEN:
        required: true
      AMS_ALEN_BOT_WEBHOOK_URL:
        required: true

env:
  AWS_REGION: us-east-1

concurrency:
  group: ${{ inputs.DestEnvironment }}-${{ inputs.AppDevName }}-${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

jobs:
  release-check:
    name: Release Check - Prod
    runs-on: ubuntu-latest
    if: inputs.DestEnvironment == 'production' && inputs.AppDevName == 'ams'
    steps:
      - name: Checkout Release Gates
        id: cd_manual_checkout_gates
        uses: actions/checkout@v4
        with:
          repository: Above-Lending/release-gates
          token: ${{ secrets.GH_TOKEN }}
          ref: 'main'
          path: tmp/release-gates

      - name: Install Python
        id: py310
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'

      - name: Release Gate
        id: cd_manual_release_gate
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}
        run: |
          ${{ steps.py310.outputs.python-path }} -m pip install -r tmp/release-gates/requirements.txt
          ${{ steps.py310.outputs.python-path }} tmp/release-gates/entrypoint.py ${{ github.sha }} 5 ${{ github.event.repository.name }}

  deploy:
    name: Deployment - ${{ inputs.DestEnvironment }}
    runs-on: codebuild-${{ inputs.DestEnvironment }}-github-runner-${{ github.run_id }}-${{ github.run_attempt }}
    if: inputs.DestEnvironment == 'production'
    environment: ${{ inputs.DestEnvironment }}
    needs: release-check
    steps:
      - uses: Above-Lending/ecs-deploy-action@v1
        with:
          DestEnvironment: ${{ inputs.DestEnvironment }}
          AppDevName: ${{ inputs.AppDevName }}
          AppName: ams

  notify-slack:
    name: Send Slack Notification
    runs-on: ubuntu-latest
    needs: [release-check, deploy]
    if: ${{ always() }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Get job status
        id: job_status
        run: |
          status=${{ needs.deploy.result }}
          case $status in
            success)
              status_message="✅ $status ✅"
              ;;
            failure)
              status_message="❌ $status ❌"
              ;;
            cancelled)
              status_message="🟠 $status 🟠"
              ;;
            *)
              status_message="⚪️ $status ⚪️"
              ;;
          esac
          echo status_message=$status_message >> "$GITHUB_OUTPUT"

      - name: Send message to Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,commit,author,action,eventName,ref
          text: "Success! Deployed to AMS [PRODUCTION]"
          author_name: 'GitHub Actions'
          custom_payload: |
            {
              "channel": "#tech-releases",
              "username": "GitHub Action",
              "text": "A new AMS release tag has been published!",
              "attachments": [
                {
                  "color": "#36a64f",
                  "fields": [
                    {
                      "title": "Release Information",
                      "value": "${{ github.ref_name }}",
                      "short": true
                    },
                    {
                      "title": "Commit SHA",
                      "value": "${{ github.sha }}",
                      "short": true
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.AMS_ALEN_BOT_WEBHOOK_URL }}

      - name: Post to chatbot's Slack channel
        uses: slackapi/slack-github-action@v1
        with:
          # Slack channel id, channel name, or user id to post message.
          # See also: https://api.slack.com/methods/chat.postMessage#channels
          # You can pass in multiple channels to post to by providing a comma-delimited list of channel IDs.
          # channels is #production-changes
          channel-id: 'C0378BK88J2'
          payload : |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "💻 ${{ github.repository_owner }}/${{ github.event.repository.name }}"
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "[${{ inputs.DestEnvironment }}] Deploy finished ${{ steps.job_status.outputs.status_message }}"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }
        env:
          SLACK_BOT_TOKEN: "${{ secrets.SLACK_BOT_TOKEN }}"

