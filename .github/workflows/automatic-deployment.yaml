name: Automatic Deployment Workflow

on:
  push:
    branches:
      - 'main'
    paths-ignore:
      - .ci/**
      - .github/**
      - CODEOWNERS
      - README*

jobs:
  cd-build:
    uses: ./.github/workflows/cd-build.yaml
    secrets: inherit
    with:
      DestEnvironment: staging

  ams-deploy-terraform-staging:
    uses: ./.github/workflows/cd-deploy-ams-ecs-staging.yaml
    needs: [cd-build]
    secrets: inherit
    with:
      DestEnvironment: staging
      AppDevName: ams

  chatbot:
    uses: ./.github/workflows/chatbot.yaml
    needs: ams-deploy-terraform-staging
    secrets: inherit
    with:
      DestEnvironment: staging
