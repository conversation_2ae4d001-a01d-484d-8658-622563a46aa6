name: Ruby

on: [push]
defaults:
  run:
    shell: bash

env:
  BUNDLE_GITHUB__COM: ${{ secrets.GH_TOKEN }}:x-oauth-basic
  BUNDLE_GEMS__CONTRIBSYS__COM: ${{ secrets.BUNDLE_GEMS__CONTRIBSYS__COM }}
  RAILS_ENV: test
  PGHOST: localhost
  PGPASSWORD: postgres
  PGUSER: postgres

jobs:
  rubocop:
    name:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
      # cache-apt-pkgs-action needs apt-fast, which needs wget and aria2
      - name: Prepare runner to use apt cache
        run: sudo apt-get -yqq update && sudo apt-get -yqq install wget aria2
      - name: Install Dependencies
        uses: awalsh128/cache-apt-pkgs-action@v1.4.3
        with:
          packages: libpq-dev ubuntu-dev-tools
          version: 1.0
      - name: Install Ruby and gems
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
      - name: Run Rubocop
        run: bundle exec rubocop --parallel
  brakeman:
    name:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      # cache-apt-pkgs-action needs apt-fast, which needs wget and aria2
      - name: Prepare runner to use apt cache
        run: sudo apt-get -yqq update && sudo apt-get -yqq install wget aria2
      - name: Install Dependencies
        uses: awalsh128/cache-apt-pkgs-action@v1.4.3
        with:
          packages: libpq-dev ubuntu-dev-tools
          version: 1.0
      - name: Install Ruby and gems
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
      - name: Run brakeman
        run: bundle exec brakeman
  erb_lint:
    name:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
      # cache-apt-pkgs-action needs apt-fast, which needs wget and aria2
      - name: Prepare runner to use apt cache
        run: sudo apt-get -yqq update && sudo apt-get -yqq install wget aria2
      - name: Install Dependencies
        uses: awalsh128/cache-apt-pkgs-action@v1.4.3
        with:
          packages: libpq-dev ubuntu-dev-tools
          version: 1.0
      - name: Install Ruby and gems
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
      - name: Run ERBLint
        run: bundle exec erb_lint .
  test:
    name:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15.10
        credentials:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
        env:
          POSTGRES_DB: abovelending_test
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis
        credentials:
            username: ${{ secrets.DOCKERHUB_USERNAME }}
            password: ${{ secrets.DOCKERHUB_TOKEN }}
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      # cache-apt-pkgs-action needs apt-fast, which needs wget and aria2
      - name: Prepare runner to use apt cache
        run: sudo apt-get -yqq update && sudo apt-get -yqq install wget aria2
      - name: Install Dependencies
        uses: awalsh128/cache-apt-pkgs-action@v1.4.3
        with:
          packages: >-
            libpq-dev
            ubuntu-dev-tools
            postgresql-client
            default-mysql-client
            libmysqlclient-dev
            libglib2.0-0
            libgconf-2-4
            libatk1.0-0
            libatk-bridge2.0-0
            libgdk-pixbuf2.0-0
            libgtk-3-0
            libgbm-dev
            libnss3-dev
            libxss-dev
            libasound2
            xvfb
            fonts-liberation
            libu2f-udev
            xdg-utils
          version: 1.0
      # Chrome driver used by system tests in rspec
      - uses: browser-actions/setup-chrome@v1
        with:
          install-chromedriver: true
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
      - name: 'Install Node packages'
        run: |
          npm install -g yarn
          yarn install --immutable
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true # runs 'bundle install' and caches installed gems automatically
      - name: Database setup
        run: bin/rails db:setup
      - name: Compile assets
        run: bin/rails assets:precompile
      - name: Run RSpec
        run: |
          mkdir /tmp/test-results
          bundle exec rspec --profile 10 --format RspecJunitFormatter --out /tmp/test-results/rspec.xml --format progress
      - name: Prepare file name for coverage
        if: failure()
        id: coverage_file_name
        run: echo "value=$(echo ${{ github.head_ref || github.ref_name }} | sed "s/[^[:alnum:].-]/_/g")" >> $GITHUB_OUTPUT
      - name: Upload coverage
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.coverage_file_name.outputs.value }}-coverage
          path: coverage
          retention-days: 1
          overwrite: true
