name: AMS ECS Deploy - Staging

on:
  workflow_call:
    inputs:
      DestEnvironment:
        type: string
        description: Destination Environment
        required: true
      AppDevName:
        type: string
        description: App name with suffix for multi-dev environments
        required: true
        default: ams
    secrets:
      GH_TOKEN:
        required: true
      SLACK_BOT_TOKEN:
        required: true

env:
  AWS_REGION: us-east-1

concurrency:
  group: ${{ inputs.DestEnvironment }}-${{ inputs.AppDevName }}-${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

jobs:
  deploy:
    name: Deployment - ${{ inputs.DestEnvironment }}
    runs-on: codebuild-${{ inputs.DestEnvironment }}-github-runner-${{ github.run_id }}-${{ github.run_attempt }}
    if: inputs.DestEnvironment == 'staging'
    environment: ${{ inputs.DestEnvironment }}
    steps:
      - uses: Above-Lending/ecs-deploy-action@v1
        with:
          DestEnvironment: ${{ inputs.DestEnvironment }}
          AppDevName: ${{ inputs.AppDevName }}
          AppName: ams

  notify-slack:
    name: Send Slack Notification
    runs-on: ubuntu-latest
    needs: [deploy]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Send message to Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,commit,author,action,eventName,ref
          text: "Success! Deployed to AMS [STAGING]"
          author_name: 'GitHub Actions'
          custom_payload: |
            {
              "channel": "#tech-releases",
              "username": "GitHub Action",
              "text": "A new AMS release tag has been published!",
              "attachments": [
                {
                  "color": "#36a64f",
                  "fields": [
                    {
                      "title": "Release Information",
                      "value": "${{ github.ref_name }}",
                      "short": true
                    },
                    {
                      "title": "Commit SHA",
                      "value": "${{ github.sha }}",
                      "short": true
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.AMS_ALEN_BOT_WEBHOOK_URL }}

  trigger-staging-tests:
    name: Trigger Integration Tests
    runs-on: ubuntu-latest
    needs: [deploy]
    steps:
      - name: Trigger staging tests
        uses: peter-evans/repository-dispatch@v3
        with:
          token: ${{ secrets.GH_TOKEN }}
          repository: Above-Lending/integration-tests-ruby
          event-type: trigger-staging-tests
          client-payload: '{"ref": "${{ github.ref }}", "sha": "${{ github.sha }}", "workflow": "ams"}'
