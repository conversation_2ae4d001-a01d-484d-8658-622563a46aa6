# frozen_string_literal: true

module Ams
  module Api
    module Loans
      class UplFinalDecision < ServiceObject
        class InvalidLoanAppStatusError < BadRequest; end
        class InvalidFinalDecisionStatus < BadRequest; end
        class InvalidProductType < BadRequest; end
        class InvalidLoanStatusForTransition < BadRequest; end
        class InvalidLoanStatusForTransition < BadRequest; end
        class InvalidBankAccountStatus < BadRequest; end

        attribute :request_id, :string
        attribute :loan_status, :string # either approved or back_end_declined
        attribute :decision_reason_number, :string
        attribute :decline_reason_text, :string
        attribute :decline_reasons, array: true
        attribute :credit_score, :integer
        attribute :score_factor, :string

        validates :request_id, :loan_status, presence: true
        validates :decision_reason_number, presence: true, if: -> { loan_status == BACK_END_DECLINED_STATUS_NAME }

        delegate :loan_inquiry, to: :loan

        DM_PRODUCT_TYPE = 'DM'
        APPROVED_STATUS_NAME = 'APPROVED'
        BACK_END_DECLINED_STATUS_NAME = 'BACK_END_DECLINED'

        def call
          call_service_object do
            validate_records
            handle_request
          end
        end

        private

        # VALIDATIONS
        def validate_records
          validate_final_decision_status
          raise RecordNotFound, "Loan with requestId #{request_id} not found" unless loan

          validate_product_type
          expire_loan! if offers_expired?
          validate_loan_status_for_transition
          validate_bank_account_link
        end

        def loan_app_status
          @loan_app_status ||= ::LoanAppStatus.for(loan_status)
        rescue ::LoanAppStatus::InvalidStatusError
          error_msg = '"status" must be one of [' \
                      "#{::LoanAppStatus::VALID_FINAL_DECISION_STATUSES.join(', ')}]"
          raise InvalidLoanAppStatusError, error_msg
        end

        def validate_final_decision_status
          return if loan_app_status.approved? || loan_app_status.back_end_declined?

          error_msg = 'Invalid final decision status. Valid statuses: ' \
                      "#{::LoanAppStatus::VALID_FINAL_DECISION_STATUSES.join(', ')}"
          raise InvalidFinalDecisionStatus, error_msg
        end

        def loan
          @loan ||= ::Loan.find_by(request_id:)
        end

        def validate_product_type
          return if loan.product_type.in? ::Loan::DIRECT_MAIL_PRODUCT_TYPES

          # TODO: currently replicating SL error message. Change the expected product type post migration
          error_message = "Unexpected loan product type #{loan.product_type} for loan " \
                          "with id #{loan.id}. Expected #{DM_PRODUCT_TYPE}"
          raise InvalidProductType, error_message
        end

        def offers_expired?
          loan.expirable_status? && loan.offers.any?(&:expired?)
        end

        def expire_loan!
          loan.expire!
          loan.reload
        end

        def validate_loan_status_for_transition
          return if loan.loan_app_status.name.in? ::LoanAppStatus::VALID_UPL_FINAL_DECISION_TRANSITION_STATUSES

          raise InvalidLoanStatusForTransition, loan_status_for_transition_err_msg
        end

        def loan_status_for_transition_err_msg
          valid_statuses_with_names =
            ::LoanAppStatus::VALID_UPL_FINAL_DECISION_TRANSITION_STATUSES.map do |status_name|
              "#{::LoanAppStatus.for(status_name).id} (#{status_name})"
            end.join(', ')

          "The transition cannot be executed because the loan #{loan.id} is in the " \
            "status #{loan.loan_app_status.id} (#{loan.loan_app_status.name}), " \
            "but it must be in one of the following: #{valid_statuses_with_names}"
        end

        def validate_bank_account_link
          return if loan.loan_app_status.name == loan_status

          bank_error_msg = 'Operation not valid. ' \
                           "There is not bank account linked to the loan with id. #{loan.unified_id}"
          raise InvalidBankAccountStatus, bank_error_msg unless loan.borrower.bank_account&.enabled
        end
        # VALIDATIONS END

        def handle_request
          if loan.loan_app_status.name == loan_status
            log_info("The loan #{loan.id} with request id #{request_id} is already on status #{loan_status}. " \
                     'Skipping Operation')
            handle_success
          elsif loan_app_status.back_end_declined?
            handle_back_end_declined
          elsif loan_app_status.approved?
            handle_approved
          end
        end

        def handle_success
          @body = { message: 'ok' }
          @status = 200
        end

        def handle_back_end_declined
          log_info("The loan #{loan.id} with request id #{request_id} is #{BACK_END_DECLINED_STATUS_NAME}")

          LoanApplications::BackEndDecline.call(
            loan:,
            decision_reason_number:,
            decline_reason_text:,
            decline_reasons:,
            credit_score:,
            score_factor:
          )

          handle_success
        end

        def handle_approved
          log_info("The loan #{loan.id} with request id #{request_id} is #{APPROVED_STATUS_NAME}")
          loan.update!(loan_app_status:)

          send_loan_approved_email
          sync_todos
          Contracts::GenerateContractJob.perform_async(loan.id)
          handle_success
        end

        def send_loan_approved_email
          Clients::CommunicationsServiceApi.send_message!(
            recipient: loan.borrower.email,
            template_key: Clients::CommunicationsServiceApi::UPL_LOAN_APPROVED_TEMPLATE,
            inputs: {
              first_name: loan.borrower.first_name
            },
            attribution: Communications::MessageAttribution.call(loan:)
          )
        end

        def sync_todos
          Gds::TriggerTodoResyncJob.perform_async(loan.request_id)
        end
      end
    end
  end
end
