# frozen_string_literal: true

module Ams
  module Api
    module Loans
      class Til < ServiceObject
        include JwtVerifiable
        include ContractHelper
        include TilHelper

        MAX_APR = 30
        MAX_APR_BY_STATE = Hash.new(MAX_APR).merge(NY: 25, PA: 25, DC: 24, MD: 24).freeze

        attribute :loan_id, :string
        validates :loan_id, presence: true

        attribute :custom_authorization, :boolean, default: false

        def call
          call_service_object(custom_authorization:) do
            verify_borrowers_active_loan!
            verify_offer_selected!
            verify_bank_account!
            verify_loan_status!

            find_or_create_contract
            @status = 200
          end
        end

        private

        def initialize_docusign_envelope(loanpro_loan:)
          verify_apr!(loanpro_loan)
          til_history = build_til_history(loanpro_loan)

          supplemental_documents = generate_supplemental_documents(til_history)
          loan_agreement_document = generate_loan_agreement_document(loanpro_loan, til_history)

          envelope_service = Contracts::GenerateDocusignEnvelope.call(loan:, loan_agreement_document:,
                                                                      supplemental_documents:)

          persist_til_history(til_history, envelope_service)
          til_history
        end

        def verify_borrowers_active_loan!
          current_borrower_loan = ::Loan.current_for_borrower_by_id(borrower.id)

          raise MethodNotAllowed, 'The loanId for this user is not valid' if loan.id != current_borrower_loan.id
        end

        def verify_bank_account!
          raise MethodNotAllowed, 'The loanId for this user does not have a bank account linked' unless bank_account
        end

        def verify_loan_status!
          return if active_approved_loan?

          raise ConflictRequest, 'This loan has already been signed' if loan.onboarded? || loan.initial_til_submit?

          loan.expire! if loan.product_type == 'IPL' && loan.loan_app_status&.name == 'APPROVED'

          raise MethodNotAllowed, 'You cannot perform this action, offers got expired or loan\'s status is incorrect'
        end

        def active_approved_loan?
          loan.loan_app_status&.name == 'APPROVED' &&
            loan.offers.any? { |offer| offer.expiration_date > Time.zone.now }
        end

        def build_til_history(loanpro_loan)
          til_data = Contracts::BuildTilData.call(loan:, loanpro_loan:)
          TilHistory.new(loan:, til_data:)
        end

        def verify_apr!(loanpro_loan)
          loanpro_loan_data = JSON.parse(loanpro_loan.loanpro_raw_response)
          apr = loanpro_loan_data.dig('LoanSetup', 'apr').to_d

          raise ServiceUnavailable, 'The TIL has APR 0% and is not available' if apr <= 0

          state_sym = borrower.latest_borrower_info&.state&.upcase&.to_sym
          return unless apr > MAX_APR_BY_STATE[state_sym]

          raise ServiceUnavailable, 'The TIL has an invalid APR for the selected state.'
        end

        def generate_loan_agreement_document(loanpro_loan, til_history)
          loanpro_loan_data = JSON.parse(loanpro_loan.loanpro_raw_response)
          ::Documents::GenerateInstallmentLoanAgreementPdf.call(loan:, til_history:, loanpro_loan_data:)
        end
      end
    end
  end
end
