# frozen_string_literal: true

module Ams
  module Api
    module Loans
      class UplTil < ServiceObject
        include JwtVerifiable
        include ContractHelper
        include TilHelper

        attribute :loan_id, :string
        validates :loan_id, presence: true

        attribute :custom_authorization, :boolean, default: false

        def call
          call_service_object(custom_authorization:) do
            verify_offer_selected!
            verify_loan_status!

            find_or_create_contract
            @status = 200
          end
        end

        private

        def verify_loan_status!
          if expired_loan?
            loan.expire!
          elsif loan.loan_app_status&.name == 'APPROVED'
            return
          end

          raise MethodNotAllowed, 'You cannot perform this action, offers got expired or loan\'s status is incorrect'
        end

        def expired_loan?
          loan.expirable_status? && loan.offers.any? { |offer| offer.expiration_date < Time.zone.now }
        end

        def initialize_docusign_envelope(loanpro_loan:)
          til_history = build_til_history(loanpro_loan)

          supplemental_documents = generate_supplemental_documents(til_history)
          loan_agreement_document = generate_loan_agreement_document(loanpro_loan, til_history)

          envelope_service = Contracts::GenerateDocusignEnvelope.call(loan:, loan_agreement_document:,
                                                                      supplemental_documents:)

          persist_til_history(til_history, envelope_service)
          til_history
        end

        def build_til_history(loanpro_loan)
          til_data = Contracts::BuildUplTilData.call(loan:, loanpro_loan:)
          TilHistory.new(loan:, til_data:)
        end

        def generate_loan_agreement_document(loanpro_loan, til_history)
          loanpro_loan_data = JSON.parse(loanpro_loan.loanpro_raw_response)
          ::Documents::GenerateUplInstallmentLoanAgreementPdf.call(loan:, til_history:, loanpro_loan_data:)
        end
      end
    end
  end
end
