# frozen_string_literal: true

module Contracts
  class VoidDocusignEnvelope < Service::Base
    class TilHistoryActiveError < StandardError; end
    class InvalidEnvelopeStatusError < StandardError; end
    class TilHistoryVoidedError < StandardError; end

    attribute :til_history, type_for(::TilHistory)

    validates :til_history, presence: true

    def call
      validate!
      validate_til_history_voidable!
      validate_docusign_envelope_voidable!

      void_docusign_envelope!
      update_records!

      self
    end

    private

    def validate_til_history_voidable!
      raise TilHistoryActiveError, 'Contract is active and cannot be voided' if til_history.latest?
      raise TilHistoryVoidedError, 'Contract is already voided' if til_history.voided_at?
    end

    def validate_docusign_envelope_voidable!
      # NOTE: declined envelopes are not voidable, but we will handle this later
      return if %w[sent delivered declined].include?(envelope_status)

      raise InvalidEnvelopeStatusError, "Envelope in status #{envelope_status} cannot be voided"
    end

    def void_docusign_envelope!
      if envelope_status == 'declined'
        Rails.logger.info("#{self.class}: Declined Envelope not Voidable",
                          til_history_id: til_history.id,
                          docusign_envelope_id: til_history.docusign_envelope_id)
        return
      end

      Rails.logger.info("#{self.class}: Voiding Contract", til_history_id: til_history.id,
                                                           docusign_envelope_id: til_history.docusign_envelope_id)

      docusign_api.void_envelope(til_history.docusign_envelope_id)
    end

    def update_records!
      til_history.update(voided_at: Time.current, deleted_at: Time.current)
      til_history.connected_loanpro_loan.update(deleted_at: Time.current)
    end

    def docusign_api
      return @docusign_api if defined? @docusign_api

      @docusign_api = Clients::DocusignApi.new
    end

    protected

    def envelope_status
      return @envelope_status if defined?(@envelope_status)

      envelope = docusign_api.get_envelope(til_history.docusign_envelope_id)
      @envelope_status = envelope.status
    end
  end
end
