# frozen_string_literal: true

module Contracts
  class GenerateDocusignEnvelope < Service::Base
    include ContractDocumentConstants
    JWT_PLACEHOLDER = 'JWT_PLACEHOLDER'

    attribute :loan, type_for(::Loan)
    attribute :loan_agreement_document, type_for(Documents::ContractDocument)
    attribute :supplemental_documents, array: true

    delegate :borrower, to: :loan

    validates :loan, :loan_agreement_document, presence: true

    attr_reader :docusign_webhook_id, :docusign_envelope_id

    def call
      validate!

      @docusign_webhook_id = SecureRandom.uuid
      @docusign_envelope_id = create_docusign_envelope

      self
    end

    private

    def docusign_webhook_builder
      @docusign_webhook_builder ||= BuildDocusignWebhookUrl.call(loan:, loan_agreement_document:,
                                                                 supplemental_documents:,
                                                                 webhook_id: docusign_webhook_id)
    end

    def create_docusign_envelope
      envelope_definition = build_envelope_definition
      docusign_api.create_envelope(envelope_definition).envelope_id
    end

    def build_envelope_definition
      docusign_api.build_envelope_definition(loan.unified_id, docusign_signer, docusign_documents,
                                             docusign_webhook_builder.url)
    end

    def docusign_api
      return @docusign_api if defined? @docusign_api

      @docusign_api = Clients::DocusignApi.new
    end

    def docusign_signer
      @docusign_signer ||= Clients::DocusignApi::Signer.new(
        email: borrower.email,
        name: "#{borrower.first_name} #{borrower.last_name}",
        recipient_id: 1,
        include_state_specific_tabs: loan.ipl?
      )
    end

    def docusign_documents
      all_contract_documents = (supplemental_documents || []) + [loan_agreement_document]
      all_contract_documents.collect do |contract_document|
        document_classification = DOCUMENT_CLASSIFICATIONS[contract_document.template_type]
        Clients::DocusignApi::Document.new(
          id: DOCUSIGN_DOCUMENT_IDS[document_classification],
          content: contract_document.content,
          name: contract_document.filename
        )
      end
    end
  end
end
