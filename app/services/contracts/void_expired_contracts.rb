# frozen_string_literal: true

module Contracts
  class VoidExpiredContracts < Service::Base
    attribute :loan, type_for(::Loan)

    validates :loan, presence: true

    def call
      validate!

      voidable_til_histories.each do |til_history|
        Contracts::VoidDocusignEnvelope.call(til_history:)
      end

      self
    end

    private

    def voidable_til_histories
      active_til = loan.loanpro_loan.til_history
      TilHistory.where(loan:, voided_at: nil).where.not(id: active_til&.id)
    end
  end
end
