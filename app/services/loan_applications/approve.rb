# frozen_string_literal: true

module LoanApplications
  class Approve < Service::Base
    include Constants::ServiceEntityNames

    attribute :loan, type_for(::Loan)
    attribute :lead, type_for(Lead)

    delegate :borrower, to: :loan

    def call
      sync_eligibility_records
      loan.update!(loan_app_status_id: ::LoanAppStatus.id(:approved),
                   contract_signing_token: loan.contract_signing_token || SecureRandom.uuid)

      Contracts::GenerateContractJob.perform_async(loan.id)
      send_contract_notifications
    end

    private

    def sync_eligibility_records
      Onboarding::SyncEligibilityData.call(loan_id: loan.id)
    end

    def send_contract_notifications
      contract_link = build_contract_link
      Contracts::SendContractApprovalSmsJob.perform_async(loan.id, contract_link)

      template_key = Clients::CommunicationsServiceApi::LOAN_APPROVED_TEMPLATE
      Clients::CommunicationsServiceApi.send_message!(recipient: borrower.email,
                                                      template_key:,
                                                      inputs: { first_name: borrower.first_name, link: contract_link })
    end

    def build_contract_link
      service_entity = LANDER_S_PARAM[lead.service_entity_name]

      base_lander_url = Rails.application.config_for(:general).lander_base_url ||
                        raise('LANDER_BASE_URL is not set')
      "#{base_lander_url}/loan-app/contract/#{loan.contract_signing_token}?s=#{service_entity}"
    end
  end
end
