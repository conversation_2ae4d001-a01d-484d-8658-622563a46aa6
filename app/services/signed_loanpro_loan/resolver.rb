# frozen_string_literal: true

module SignedLoanproLoan
  class Resolver < Service::Base
    attribute :loan, type_for(::Loan)

    # Resolves the correct signed LoanPro loan (loanpro_loan) for an onboarded loan from LoanPro.
    #
    # This resolver supports two modes, controlled by feature flags:
    #   - servicing_loanpro_loan_from_api — fetches the active loan from LoanPro API (source of truth)
    #   - servicing_loanpro_loan_self_healing — triggers self-healing if AMS loanpro_loan doesn't match LoanPro
    #
    # Returns `self` and exposes:
    #   - loanpro_loan: the resolved AMS or API-backed record
    #   - meta: structured metadata including IDs, source info, healing status, and mismatches
    def call
      self_heal! if self_healing_needed?

      self
    end

    def loanpro_loan
      return active_loanpro_loan if servicing_loanpro_loan_from_api?

      ams_loanpro_loan
    end

    def meta
      ams_loanpro_loan_id = ams_loanpro_loan&.id
      api_loanpro_loan_id = active_loanpro_loan&.id

      {
        ams_loanpro_loan_id:,
        api_loanpro_loan_id:,
        api_loanpro_loan_error: @active_loanpro_loan_error,
        is_loanpro_loan_ids_matched: ams_loanpro_loan_id.present? && ams_loanpro_loan_id == api_loanpro_loan_id,
        is_servicing_loanpro_loan_from_api_enabled: servicing_loanpro_loan_from_api?,
        is_servicing_loanpro_loan_self_healing_enabled: servicing_loanpro_loan_self_healing?,
        is_self_healing_needed: self_healing_needed?
      }.merge(heal_meta).compact
    end

    private

    def self_healing_needed?
      return @self_healing_needed if defined? @self_healing_needed

      return false unless servicing_loanpro_loan_self_healing?
      return true if active_loanpro_loan.nil?

      @self_healing_needed = ams_loanpro_loan&.id != active_loanpro_loan&.id
    end

    def self_heal!
      healer = SelfHealer.call(loan:)

      @heal_meta = healer.meta.merge(is_servicing_loanpro_loan_healed: true)
      @ams_loanpro_loan = healer.ams_loanpro_loan
    end

    def heal_meta
      return @heal_meta if defined? @heal_meta

      @heal_meta = {}
    end

    def servicing_loanpro_loan_from_api?
      Flipper.enabled?(:servicing_loanpro_loan_from_api)
    end

    def servicing_loanpro_loan_self_healing?
      Flipper.enabled?(:servicing_loanpro_loan_self_healing)
    end

    def ams_loanpro_loan
      return @ams_loanpro_loan if defined? @ams_loanpro_loan

      @ams_loanpro_loan = LoanproLoan.where(loan_id: loan.id).latest_signed.first
    end

    def active_loanpro_loan
      return @active_loanpro_loan if defined? @active_loanpro_loan

      @active_loanpro_loan ||= Servicing::LoadActiveLoanproLoanFromApi.call(unified_id: loan.unified_id)
    rescue StandardError => e
      @active_loanpro_loan_error = e.message
      @active_loanpro_loan = nil
    end
  end
end
