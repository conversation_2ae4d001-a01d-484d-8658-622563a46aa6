# frozen_string_literal: true

module Servicing
  class BaseController < ApplicationController
    include DomainRoutable
    include WhoopsTrackable
    include Authenticatable
    include MaintenanceModeEnforceable
    include OriginationsFunnelRoutable

    before_action :set_event_agent_user
    before_action :initialize_view, if: -> { request.get? }
    prepend_before_action :ensure_authenticated_borrower!

    protected

    # Overriding base name to avoid name collisions
    def request_event_name
      method = request.method.downcase
      "#{method}_servicing_#{controller_name}_#{action_name}"
    end

    def default_meta
      { agent: @event_agent, code: session[:code] }
    end

    def handle_servicing_error(error)
      log_exception(error)

      raise if whoops_action?

      redirect_to_whoops_path(message: error.message, redirect_path: :whoops_servicing_dashboard_index_path)
    end

    private

    def initialize_view
      @header_menu = true
      @footer_contact_info = true
    end

    def loanpro_loan
      signed_loanpro_loan_resolver.loanpro_loan
    end

    def signed_loanpro_loan_resolver
      @signed_loanpro_loan_resolver ||= SignedLoanproLoan::Resolver.call(loan: current_loan)
    end
  end
end
