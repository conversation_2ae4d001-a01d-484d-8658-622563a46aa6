# frozen_string_literal: true

module Servicing
  class DashboardController < BaseController
    skip_before_action :ensure_authenticated_borrower!, only: :whoops
    skip_before_action :ensure_correct_funnel_step!, only: :whoops

    def index
      initialize_dashboard

      # This session entry is used to hide the Make a Payment button when the user is
      # logged in and on the home page or other static pages, where we don't want to have
      # inline API calls to Dash.
      # Should be revisited when/if <PERSON> is migrated to AMS and we can read this state directly
      # off the database.
      #
      session[:servicing_loan_payable] = !@details.debt_sale? && !@details.paid_off?
    rescue StandardError => e
      handle_servicing_error(e)
    ensure
      RecordRequestEvent.call(request_event_name:, request:, response:, meta: index_meta)
    end

    def whoops
    ensure
      RecordRequestEvent.call(request_event_name:, request:, response:, meta: whoops_meta)
    end

    private

    def index_meta
      default_meta
        .merge(signed_loanpro_loan_resolver.meta)
        .merge(
          is_servicing_loan_payable: session[:servicing_loan_payable] || false
        ).compact
    end

    def initialize_dashboard
      initialize_dashboard_details
      initialize_payment_profiles
      initialize_payment_history
      initialize_upcoming_payments
    rescue Clients::DashServicingApi::Error
      # This API call will error out if the user doesn't have autopay enabled. Until this is properly
      # handled on the dash side, we need to handle this with a blanket rescue.
    end

    def initialize_dashboard_details
      @details = Loanpro::DashboardDetails.call(borrower: current_borrower, loan_id: loanpro_loan&.loanpro_loan_id)
    end

    def initialize_payment_profiles
      @payment_profiles = Loanpro::PaymentProfiles.call(loanpro_loan_id: loanpro_loan&.loanpro_loan_id)
    end

    def initialize_payment_history
      @payment_history = Loanpro::PaymentHistory.call(loan_id: loanpro_loan&.loanpro_loan_id)
    end

    def initialize_upcoming_payments
      @upcoming_payments = Loanpro::UpcomingPayments.call(loan_id: loanpro_loan&.loanpro_loan_id)
    end
  end
end
