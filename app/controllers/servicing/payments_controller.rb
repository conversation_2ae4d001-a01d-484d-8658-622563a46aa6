# frozen_string_literal: true

module Servicing
  class PaymentsController < BaseController
    before_action :initialize_history_range_form_model, only: :index
    before_action :initialize_new_payment_form_model, only: %i[new create]
    before_action :ensure_payment_capability, except: :index

    def index
      @payment_history = Loanpro::PaymentHistory.call(loan_id: loanpro_loan&.loanpro_loan_id)
      # NILE-632
      @show_breakdown = Flipper.enabled?(:show_servicing_payment_breakdown)
    rescue StandardError => e
      handle_servicing_error(e)
    ensure
      RecordRequestEvent.call(request_event_name:, request:, response:, meta: index_meta)
    end

    def new
      set_new_payment_form_model_constraints
    rescue StandardError => e
      handle_servicing_error(e)
    ensure
      RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta)
    end

    def create
      set_new_payment_form_model_constraints

      return unless @form_model.valid?
      return render :new if @form_model.show_duplicate_warning_modal?

      process_create_payment
      redirect_to success_servicing_payments_path
    rescue StandardError => e
      handle_servicing_error(e)
    ensure
      render :new, status: :unprocessable_entity unless performed?

      RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta, form_model: @form_model)
    end

    def success
      return redirect_to new_servicing_payment_path unless request.referrer&.include?(new_servicing_payment_path)

      initialize_success
      return redirect_to new_servicing_payment_path unless @upcoming_payments.present?

      @payment_profile = @payment_profiles.active
      @last_payment = @upcoming_payments.latest_payment
    rescue StandardError => e
      handle_servicing_error(e)
    ensure
      RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta, form_model: @form_model)
    end

    def destroy
      payment_id = params[:id]
      @cancelled_payment = cancel_payment(payment_id)
      return redirect_to servicing_dashboard_index_path if @cancelled_payment

      render status: :unprocessable_entity
    rescue StandardError => e
      handle_servicing_error(e)
    ensure
      RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta)
    end

    private

    def index_meta
      default_meta.merge(signed_loanpro_loan_resolver.meta).compact
    end

    def initialize_history_range_form_model # rubocop:disable Metrics/AbcSize
      values = params.require(:servicing_history_range_form_model).permit(*HistoryRangeFormModel.attribute_names)

      if values.present? && values.compact_blank.blank?
        return redirect_to request.path, params: params.except(:servicing_history_range_form_model)
      end

      @form_model = HistoryRangeFormModel.new(
        **params.require(:servicing_history_range_form_model)
          .permit(*HistoryRangeFormModel.attribute_names)
      )

      redirect_to request.path, params: params.except(:servicing_history_range_form_model) unless @form_model.valid?
    rescue ActionController::ParameterMissing
      @form_model = HistoryRangeFormModel.new
    end

    def initialize_new_payment_form_model
      @form_model = NewPaymentFormModel.new(
        **params.require(:servicing_new_payment_form_model)
          .permit(*NewPaymentFormModel.attribute_names)
      )
    rescue ActionController::ParameterMissing
      @form_model = NewPaymentFormModel.new(override_duplicate_warning: true)
    end

    def process_create_payment
      if Flipper.enabled?(:ams_create_payment)
        Loanpro::CreatePayment.call(
          loanpro_loan_id: loanpro_loan&.loanpro_loan_id,
          amount: @form_model.payment_amount,
          apply_date: @form_model.payment_date.strftime('%F'),
          payment_profile_id: @form_model.payment_profile_id,
          apply_timezone: @form_model.payment_time_zone,
          charge_off_recovery: @form_model.is_charged_off
        )
      else
        Clients::DashServicingApi.create_payment(borrower: current_borrower,
                                                 body: @form_model.create_payment_attributes)
      end
      session[:last_scheduled_payment_amount] = @form_model.payment_amount
      session[:last_scheduled_payment_date] = @form_model.payment_date
    end

    def initialize_success
      initialize_payment_profiles

      begin
        initialize_upcoming_payments
      rescue Clients::DashServicingApi::Error
        # This API call will error out if the user doesn't have autopay enabled. Until this is properly
        # handled on the dash side, we need to handle this with a blanket rescue.
      end
    end

    def initialize_upcoming_payments
      @upcoming_payments = Loanpro::UpcomingPayments.call(loan_id: loanpro_loan&.loanpro_loan_id)
    end

    def initialize_payment_profiles
      @payment_profiles = Loanpro::PaymentProfiles.call(loanpro_loan_id: loanpro_loan&.loanpro_loan_id)
    end

    def cancel_payment(payment_id)
      @cancelled_payment = Loanpro::CancelPayment.call(borrower: current_borrower, autopay_id: payment_id)
    end

    def set_new_payment_form_model_constraints # rubocop:disable Metrics/AbcSize,Metrics/MethodLength
      @form_model.assign_attributes(
        borrower_name: dashboard_details.borrower_name,

        # Status validation
        is_past_due: dashboard_details.past_due?,
        is_charged_off: dashboard_details.charged_off?,

        # Amount validation
        overdue_amount: dashboard_details.overdue_amount,
        last_payment_amount: dashboard_details.last_payment&.payment_amount,
        next_payment_amount: dashboard_details.next_payment_amount,
        payoff_amount: dashboard_details.payoff_amount,

        # Duplicate checks
        last_scheduled_payment_amount: session[:last_scheduled_payment_amount],
        last_scheduled_payment_date: session[:last_scheduled_payment_date],

        # Bank account validation
        payment_profile_id: payment_profiles.active&.id,
        bank_account: payment_profiles.active&.label
      )
      @form_model.validate_attribute(:bank_account)

      # if the day after the contract date is in the future, that should be used as minimum payment date
      @form_model.min_payment_date = [dashboard_details.contract_date + 1.day, DateHelper.time_in_ct.to_date].max

      # if we have a user timezone, we should set its current date as the minimum date
      if @form_model.payment_time_zone.present?
        user_today = Time.now.in_time_zone(@form_model.payment_time_zone).to_date

        @form_model.min_payment_date = [@form_model.min_payment_date, user_today].max
      end

      # if the loan is past due, apply default payment option
      if @form_model.payment_option.blank? && dashboard_details.past_due?
        @form_model.assign_attributes(payment_option: 'past_due_amount',
                                      payment_amount: dashboard_details.overdue_amount)
      end

      # if the loan is charged-off it won't have a next payment date
      @form_model.max_payment_date = [dashboard_details.next_payment_date, 1.year.from_now].compact.min
    end

    def ensure_payment_capability
      redirect_to servicing_dashboard_index_path unless session[:servicing_loan_payable]
    end

    def payment_profiles
      @payment_profiles ||= Loanpro::PaymentProfiles.call(loanpro_loan_id: loanpro_loan&.loanpro_loan_id)
    end

    def dashboard_details
      @dashboard_details ||= Loanpro::DashboardDetails.call(borrower: current_borrower,
                                                            loan_id: loanpro_loan&.loanpro_loan_id)
    end
  end
end
