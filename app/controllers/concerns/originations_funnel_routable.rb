# frozen_string_literal: true

module OriginationsFunnelRoutable
  extend ActiveSupport::Concern

  included do
    before_action :initialize_session, if: -> { request.get? }
    before_action :ensure_correct_funnel_step!, if: -> { request.get? }
  end

  PATH_BY_STATUS = {
    # PI1
    ::LoanAppStatus::EXPIRED_STATUS => :basic_info_loan_applications_path,
    ::LoanAppStatus::WITHDRAWN_STATUS => :basic_info_loan_applications_path,
    # PI2
    ::LoanAppStatus::BASIC_INFO_COMPLETE_STATUS => :additional_info_loan_applications_path,
    # Offer
    ::LoanAppStatus::ADD_INFO_COMPLETE_STATUS => :select_offer_loan_applications_path,
    ::LoanAppStatus::DEBT_RELIEF_SHOWN_STATUS => :select_offer_loan_applications_path,
    ::LoanAppStatus::OFFERED_STATUS => :select_offer_loan_applications_path,
    # Bank
    #
    # Handling for bank account routes is directly in #path_by_status as we don't have status codes for those
    #
    # Todo
    ::LoanAppStatus::BANK_SUBMIT_STATUS => :todos_path,
    ::LoanAppStatus::READY_FOR_REVIEW_STATUS => :todos_path,
    ::LoanAppStatus::AUTO_VERIFICATION_PROCESSING_STATUS => :todos_path,
    ::LoanAppStatus::PENDING_STATUS => :todos_path,
    # Til
    ::LoanAppStatus::APPROVED_STATUS => :contracts_path,
    # Congrats
    ::LoanAppStatus::INITIAL_TIL_SUBMIT_STATUS => :congratulations_contracts_path,
    # AA
    ::LoanAppStatus::FRONT_END_DECLINED_STATUS => :no_offer_exit_pages_path,
    ::LoanAppStatus::BACK_END_DECLINED_STATUS => :adverse_actions_path,
    # Dashboard
    ::LoanAppStatus::ONBOARDED_STATUS => :servicing_dashboard_index_path
  }.freeze

  def ensure_correct_funnel_step!
    # TODO: Remove this early return in favor of allowing specific routes without loans (intake, etc)
    #
    return unless current_loan.present?

    return if path_by_status.to_s.include?(request.path)

    redirect_to path_by_status if path_by_status.present?
  end

  def redirect_to_whoops_path(message:, redirect_path: :whoops_exit_pages_path, redirect_params: {})
    flash[:whoops_data] = {
      message: message.to_s.truncate(150),
      request_id: Rails.logger.try(:named_tags).try(:[], :request_id) || 'unknown_request_id'
    }

    redirect_to send(redirect_path, redirect_params.merge(offer: session[:code], s: session[:service_entity]))
  end

  def initialize_session
    if current_loan.present?
      session[:code] = current_loan.code

      if (lead = current_loan.lead)
        session[:service_entity] = Constants::ServiceEntityNames::LANDER_S_PARAM[lead.service_entity_name]
      end
    else
      extract_from_query_into_session(offer: :code, s: :service_entity)
    end
  end

  def resolver
    @resolver ||= LoanApplications::Resolver.new(code: offer_code, loan_id: current_loan&.id)
  end

  private

  def path_by_status # rubocop:disable Metrics/AbcSize,Metrics/CyclomaticComplexity
    return if resolver.loan_app_status == ::LoanAppStatus::EXPIRED_STATUS && reapply_flow_paths.include?(request.path)

    if resolver.loan_app_status == ::LoanAppStatus::BASIC_INFO_COMPLETE_STATUS
      return path_for_basic_info_complete_status
    end

    return path_for_pending_status if resolver.loan_app_status == ::LoanAppStatus::PENDING_STATUS

    return path_for_approved_status if resolver.loan_app_status == ::LoanAppStatus::APPROVED_STATUS

    return path_for_onboarded_status if resolver.loan_app_status == ::LoanAppStatus::ONBOARDED_STATUS

    expected_path = PATH_BY_STATUS[resolver.loan_app_status]
    return signout_borrowers_path if expected_path.blank?

    send(expected_path, query_vars)
  end

  def reapply_flow_paths
    @reapply_flow_paths ||= [
      reapply_loan_applications_path,
      continue_create_loan_applications_path,
      continue_loan_applications_path,
      continue_edit_loan_applications_path,
      continue_save_loan_applications_path
    ].freeze
  end

  # For BASIC_INFO_COMPLETE status, we redirect to credit freeze exit page if credit freeze is active
  # otherwise we fallback to the expected step.
  def path_for_basic_info_complete_status
    return credit_freeze_loan_applications_path(query_vars) if resolver.credit_freeze_active?

    fallback_path
  end

  # For PENDING status, we allow all bank account routes, with a
  # redirect to the expected step.
  #
  def path_for_pending_status
    return manual_bank_accounts_path(query_vars) if request.path.include?(manual_bank_accounts_path)
    return bank_accounts_path(query_vars) unless resolver.loan_bank_accounts.present?
    return select_bank_accounts_path(query_vars) unless resolver.loan_has_enabled_bank_account?

    fallback_path
  end

  # For APPROVED status, we only allow redirecting once we have a LoanPro Loan.
  #
  def path_for_approved_status
    return todos_path(query_vars) unless resolver.contract_generated?

    fallback_path
  end

  # For ONBOARDED status, we allow all dashboard routes
  def path_for_onboarded_status
    return if request.path.include?(servicing_dashboard_index_path)

    fallback_path
  end

  def fallback_path
    path = PATH_BY_STATUS[resolver.loan_app_status]
    send(path, query_vars)
  end

  def query_vars
    { offer: offer_code, s: service_entity }
  end

  def service_entity
    session[:service_entity] || params[:s]
  end

  def offer_code
    session[:code] || params[:offer]
  end
end
