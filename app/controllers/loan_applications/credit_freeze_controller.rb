# frozen_string_literal: true

module LoanApplications
  class Credit<PERSON>reezeController < BaseController
    include MaintenanceModeEnforceable

    def credit_freeze
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta)
    end

    def credit_freeze_resubmit
      credit_freeze_resubmit_service.call

      reset_offer_wait_session_data
      redirect_to select_offer_loan_applications_path(offer: session[:code], s: session[:service_entity])
    rescue CreditFreezeResubmit::CreditFreezeNotActiveError => e
      handle_credit_freeze_not_active_error(e)
    rescue StandardError => e
      handle_unexpected_error(e)
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: credit_freeze_resubmit_meta)
    end

    protected

    def credit_freeze_resubmit_meta
      @credit_freeze_active = true if @credit_freeze_active.nil?

      default_meta.merge(
        is_in_maintenance_mode: in_maintenance_mode?,
        credit_freeze_active: @credit_freeze_active
      ).merge(credit_freeze_resubmit_service.meta)
    end

    private

    def credit_freeze_resubmit_service
      @credit_freeze_resubmit_service ||= LoanApplications::CreditFreezeResubmit.new(loan: current_loan)
    end

    def reset_offer_wait_session_data
      # Clear the offer poll start timestamp to prevent routing user to application processing page after a delayed resubmission
      session.delete(:offer_wait_at)
    end

    def handle_credit_freeze_not_active_error(error)
      @credit_freeze_active = false

      Rails.logger.error(message: 'Credit freeze not active.', errors: [error.message], loan_id: current_loan&.id)
      redirect_to resume_borrowers_path
    end
  end
end
