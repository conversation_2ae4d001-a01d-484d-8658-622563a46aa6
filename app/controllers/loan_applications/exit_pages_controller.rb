# frozen_string_literal: true

module LoanApplications
  class ExitPagesController < BaseController
    skip_before_action :ensure_authenticated_borrower!
    skip_before_action :ensure_correct_funnel_step!

    def active_application
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta)
    end

    def application_processing
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta)
    end

    def no_offer
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta)
    end

    def thank_you
      @footer_contact_info = true
      @footer_credibility = false
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta)
    end

    def whoops
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: whoops_meta)
    end
  end
end
