# frozen_string_literal: true

module Borrowers
  class AccountsController < BaseController
    before_action :initialize_create_account_form_model, only: %i[upl_account_new upl_account_create]
    before_action :ensure_borrower_email!, only: %i[account_setup resend_welcome_email]
    before_action :ensure_active_loan!, only: %i[account_setup resend_welcome_email]

    layout 'application'

    def upl_account_new
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta, form_model: @form_model)
    end

    def upl_account_create # rubocop:disable Metrics/AbcSize
      return unless @form_model.valid?

      LoanApplications::AppFromInquiry.call(@form_model.create_account_attributes)

      borrower = Users::SignIn.call(**@form_model.sign_in_attributes).borrower

      sign_in(borrower)

      redirect_to resume_borrowers_path
    rescue LoanApplications::AppFromInquiry::ExistingLoanForEmail
      redirect_to signin_borrowers_path(message: 'ongoing_loan')
    rescue Auth::AuthenticationError, ActiveModel::ValidationError
      @message_level = :error
      @message = 'Incorrect username or password.'
    rescue StandardError => e
      log_exception(e)

      @message_level = :error
      @message = 'An error has occurred. Please refresh the page and try again.'
    ensure
      render :upl_account_new, status: :unprocessable_entity unless performed?

      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta, form_model: @form_model)
    end

    def account_setup
      render layout: 'blank'
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta)
    end

    def resend_welcome_email
      Users::SendWelcomeEmail.call(email: resolver.borrower_email)

      respond_to do |format|
        format.turbo_stream
        format.html { render :account_setup, layout: 'blank' }
      end
    rescue StandardError => e
      log_exception(e)

      redirect_to_whoops_path(message: e.message)
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta)
    end

    private

    def initialize_and_validate_loan_inquiry
      @loan_inquiry = ::LoanInquiry.find_by(id: params[:loan_inquiry_id])
      @loan_inquiry = nil if @loan_inquiry&.expired?

      return render :upl_account_new_expired, status: :unprocessable_entity if @loan_inquiry.blank?

      render 'verification_documents/adverse_actions/index', status: :unprocessable_entity if @loan_inquiry.decline
    end

    def initialize_create_account_form_model
      initialize_and_validate_loan_inquiry

      @form_model = CreateAccountFormModel.new(
        **params.require(:borrowers_create_account_form_model)
                .permit(*CreateAccountFormModel.attribute_names)
      )
    rescue ActionController::ParameterMissing
      @form_model = CreateAccountFormModel.new

      if @loan_inquiry.present?
        @form_model.assign_attributes(
          loan_inquiry_id: @loan_inquiry.id,
          email: @loan_inquiry.application.[]('email')
        )
      end
    end

    def ensure_borrower_email!
      return if resolver.borrower_email.present?

      query = { offer: session[:code], s: session[:service_entity] }
      redirect_to intake_loan_applications_path(query)
    end

    def ensure_active_loan!
      return if resolver.loan_active?

      query = { offer: session[:code], s: session[:service_entity] }
      redirect_to basic_info_loan_applications_path(query)
    end
  end
end
