# frozen_string_literal: true

module Contracts
  class ContractGenerationError < StandardError; end

  class GenerateContractJob < ApplicationJob
    sidekiq_options queue: 'critical', tags: %w[loans contracts], retry: 5, lock: :until_executed, lock_ttl: 1.minute,
                    on_conflict: :log

    attr_reader :loan

    def perform(loan_id)
      @loan = ::Loan.find(loan_id)

      generate_til

      notify_async_event(
        name: event_name,
        success: true,
        meta: generate_contract_meta(loan_id)
      )
    rescue StandardError => e
      Rails.logger.error "Failed to generate a contract for loan #{loan_id}: #{e.message}"

      notify_async_event(
        name: event_name,
        success: false,
        fail_reason: e.message,
        meta: generate_contract_meta(loan_id).merge(error_class: e.class)
      )

      log_exception(e)
      raise e
    end

    def generate_contract_meta(loan_id)
      {
        loan_id:,
        unified_id: loan&.unified_id,
        has_loan: loan.present?,
        product_type: loan&.product_type
      }
    end

    private

    def generate_til
      case loan.product_type
      when ::Loan::IPL_LOAN_PRODUCT_TYPE
        generate_ipl_til
      when ::Loan::UPL_LOAN_PRODUCT_TYPE
        generate_upl_til
      else
        raise 'Unknown product type'
      end
    end

    def generate_ipl_til
      til_service = Ams::Api::Loans::Til.new(loan_id: loan.id, custom_authorization: true)
      til_service.call
      raise ContractGenerationError, til_service.body unless til_service.status == 200
    end

    def generate_upl_til
      til_service = Ams::Api::Loans::UplTil.new(loan_id: loan.id, custom_authorization: true)
      til_service.call
      raise ContractGenerationError, til_service.body unless til_service.status == 200
    end
  end
end
