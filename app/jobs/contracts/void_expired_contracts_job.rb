# frozen_string_literal: true

module Contracts
  class VoidExpiredContractsJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[loans contracts], retry: 5

    def perform(loan_id)
      loan = ::Loan.find(loan_id)
      Contracts::VoidExpiredContracts.call(loan:)

      notify_async_event(
        name: event_name,
        success: true,
        meta: { loan_id: }
      )

      Rails.logger.info('Voided expired contracts', loan_id:)
    rescue StandardError => e
      Rails.logger.error('Failed to void expired contracts', class: self.class, loan_id:, exception: e)
      ExceptionLogger.error(e)

      notify_async_event(
        name: event_name,
        success: false,
        fail_reason: e.message,
        meta: { error_class: e.class, loan_id: }
      )
      raise e
    end
  end
end
