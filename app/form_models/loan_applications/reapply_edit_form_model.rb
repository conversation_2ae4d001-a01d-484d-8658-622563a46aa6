# frozen_string_literal: true

module LoanApplications
  class ReapplyEditFormModel < ApplicationFormModel # rubocop:disable Metrics/ClassLength
    attribute :first_name
    attribute :last_name
    attribute :phone_number
    attribute :ssn, :string

    attribute :date_of_birth_day
    attribute :date_of_birth_month
    attribute :date_of_birth_year

    attribute :address_street
    attribute :address_apt
    attribute :city
    attribute :state
    attribute :zip_code

    attribute :married, :boolean, default: false
    attribute :spouse_first_name
    attribute :spouse_last_name
    attribute :spouse_different_address, :boolean, default: false
    attribute :spouse_address_street
    attribute :spouse_address_apt
    attribute :spouse_city
    attribute :spouse_state
    attribute :spouse_zip_code

    attribute :employment_annual_income
    attribute :employment_pay_frequency, :string
    attribute :employment_status, :string
    attribute :housing_monthly_payment

    normalize :phone_number, with: :normalize_positive_integer
    normalize :ssn, with: :normalize_positive_integer

    normalize :first_name, with: :normalize_string
    normalize :last_name, with: :normalize_string
    normalize :address_street, with: :normalize_string
    normalize :city, with: :normalize_string

    normalize :spouse_first_name, with: :normalize_string
    normalize :spouse_last_name, with: :normalize_string
    normalize :spouse_address_street, with: :normalize_string
    normalize :spouse_city, with: :normalize_string

    normalize :housing_monthly_payment, with: :normalize_float
    normalize :employment_annual_income, with: :normalize_float

    validates :first_name, presence: { message: 'First Name is required' },
                           format: {
                             with: /\A^([A-Za-z][ '-]?)+$\z/,
                             message: 'A valid first name is required'
                           }
    validates :last_name, presence: { message: 'Last Name is required' },
                          format: {
                            with: /\A^([A-Za-z][ '-]?)+$\z/,
                            message: 'A valid last name is required'
                          }

    validates :phone_number, presence: { message: 'A valid phone number is required' }

    validates :phone_number, numericality: { greater_than_or_equal_to: 1_000_000_000,
                                             message: 'Phone number must be 10 digits long' }

    validates :phone_number, numericality: { greater_than_or_equal_to: 2_000_000_000,
                                             message: 'Phone number cannot start with 1' }

    validates :phone_number, numericality: { less_than_or_equal_to: 9_999_999_999,
                                             message: 'Phone number must be 10 digits long' }

    validates :address_street, presence: { message: 'Street address is required' }
    validates :city, presence: { message: 'City is required' },
                     format: { with: /\A[^0-9]*\z/, message: 'City cannot include digits' }

    validates :state, presence: { message: 'State is required' }
    validates :zip_code, presence: { message: 'Zip Code is required' },
                         length: { is: 5, message: 'A 5 digit Zip Code is required' },
                         format: { with: /\A[0-9]{5}\z/, message: 'Zip Code can only include digits' }

    validates :ssn,
              presence: { message: 'Social Security Number is required' },
              length: { is: 9 },
              format: { with: /\A\d{9}\z/, message: 'is not a valid format' }

    validates :date_of_birth_day, presence: true
    validates :date_of_birth_month, presence: true
    validates :date_of_birth_year, presence: true

    validates :housing_monthly_payment,
              presence: { message: 'Please enter your monthly housing payment' },
              numericality: {
                greater_than_or_equal_to: 0,
                message: 'must be greater than or equal to $0'
              }

    validates :employment_status,
              presence: { message: 'Please select an employment status' },
              inclusion: { in: ::Loan.employment_statuses.keys }
    validates :employment_pay_frequency,
              presence: { message: 'Please select a payment frequency' },
              inclusion: { in: ::Loan.employment_pay_frecuencies.keys }

    validates :employment_annual_income, # :income in additioanl info
              presence: { message: 'Please enter your income' },
              numericality: {
                greater_than_or_equal_to: 0,
                less_than_or_equal_to: 1_000_000,
                message: 'must be between $0 and $1,000,000'
              }

    validate :valid_date_of_birth
    validate :validate_no_po_box_in_address

    with_options if: :married? do
      validates :spouse_first_name, presence: { message: 'First Name is required' },
                                    format: { with: /\A[^0-9]*\z/, message: 'First Name cannot include digits' }
      validates :spouse_last_name, presence: { message: 'Last Name is required' },
                                   format: { with: /\A[^0-9]*\z/, message: 'Last Name cannot include digits' }
    end

    with_options if: :spouse_different_address? do
      validates :spouse_address_street, presence: { message: 'Address is required' }
      validates :spouse_city, presence: { message: 'City is required' },
                              format: { with: /\A[^0-9]*\z/, message: 'City cannot include digits' }
      validates :spouse_state, presence: { message: 'State is required' }
      validates :spouse_zip_code, presence: { message: 'Zip code is required' },
                                  length: { is: 5, message: 'A 5 digit Zip Code is required' },
                                  format: { with: /\A[0-9]{5}\z/, message: 'Zip Code can only include digits' }

      validate :validate_no_po_box_in_spouse_address
    end

    def initialize(attributes = {})
      super

      clear_spouse_name_if_needed
      clear_spouse_address_if_needed
    end

    def reapplication_attributes
      attributes.slice(*%w[first_name last_name phone_number ssn address_street address_apt city state zip_code
                           date_of_birth_day date_of_birth_month date_of_birth_year housing_monthly_payment
                           employment_annual_income employment_pay_frequency employment_status married])
                .merge(spouse_attributes.transform_keys { |key| "spouse_#{key}" })
                .symbolize_keys
    end

    def spouse_attributes
      address = attributes.slice('address_street', 'city', 'state', 'zip_code') if married && !spouse_different_address

      {
        first_name: spouse_first_name,
        last_name: spouse_last_name,
        address_street: spouse_address_street,
        city: spouse_city,
        state: spouse_state,
        zip_code: spouse_zip_code
      }.merge(address&.symbolize_keys || {})
    end

    def date_of_birth
      return if date_of_birth_day.blank? || date_of_birth_month.blank? || date_of_birth_year.blank?

      @date_of_birth = Date.new(date_of_birth_year.to_i, date_of_birth_month.to_i, date_of_birth_day.to_i)
    end

    def date_of_birth=(value)
      date = Time.parse(value.to_s)

      self.date_of_birth_day = date.day
      self.date_of_birth_month = date.month
      self.date_of_birth_year = date.year
    end

    private

    def clear_spouse_name_if_needed
      return if married?

      self.spouse_first_name = nil
      self.spouse_last_name = nil
      self.spouse_different_address = false
    end

    def clear_spouse_address_if_needed
      return if spouse_different_address?

      self.spouse_address_street = nil
      self.spouse_city = nil
      self.spouse_state = nil
      self.spouse_zip_code = nil
    end

    def valid_date_of_birth
      return errors.add(:date_of_birth, 'Date of Birth is required') if date_of_birth.blank?
      return errors.add(:date_of_birth, 'must be at least 17 years old') if date_of_birth.after?(17.years.ago)

      errors.add(:date_of_birth, 'must be less than 110 years old') if date_of_birth.before?(110.years.ago)
    end

    def validate_no_po_box_in_address
      return unless contains_po_box?(address_street)

      errors.add(:address_street, 'Please provide a valid residential address. P.O. Boxes are not accepted.')
    end

    def validate_no_po_box_in_spouse_address
      return unless contains_po_box?(spouse_address_street)

      errors.add(:spouse_address_street, 'Please provide a valid residential address. P.O. Boxes are not accepted.')
    end

    def contains_po_box?(value)
      value.to_s.gsub(/[[:punct:]]|[[:space:]]/, '').downcase.include?('pobox')
    end

    def married? = married

    def spouse_different_address? = spouse_different_address
  end
end
