# frozen_string_literal: true

module LoanApplications
  class BasicInfoFormModel < ApplicationFormModel
    attribute :first_name
    attribute :last_name
    attribute :phone_number
    attribute :password

    attribute :address_street
    attribute :city
    attribute :state
    attribute :zip_code

    attribute :married, :boolean, default: false
    attribute :spouse_address_street
    attribute :spouse_city
    attribute :spouse_different_address, :boolean, default: false
    attribute :spouse_first_name
    attribute :spouse_last_name
    attribute :spouse_state
    attribute :spouse_zip_code

    attribute :tcpa_accepted, :boolean, default: false

    normalize :phone_number, with: :normalize_positive_integer

    normalize :first_name, with: :normalize_string
    normalize :last_name, with: :normalize_string
    normalize :address_street, with: :normalize_string
    normalize :city, with: :normalize_string

    normalize :spouse_first_name, with: :normalize_string
    normalize :spouse_last_name, with: :normalize_string
    normalize :spouse_address_street, with: :normalize_string
    normalize :spouse_city, with: :normalize_string

    validates :first_name, presence: { message: 'First Name is required' },
                           format: {
                             with: /\A^([A-Za-z][ '-]?)+$\z/,
                             message: 'A valid first name is required'
                           }
    validates :last_name, presence: { message: 'Last Name is required' },
                          format: {
                            with: /\A^([A-Za-z][ '-]?)+$\z/,
                            message: 'A valid last name is required'
                          }

    validates :phone_number, presence: { message: 'A valid phone number is required' }

    validates :phone_number, numericality: { greater_than_or_equal_to: 1_000_000_000,
                                             message: 'Phone number must be 10 digits long' }

    validates :phone_number, numericality: { greater_than_or_equal_to: 2_000_000_000,
                                             message: 'Phone number cannot start with 1' }

    validates :phone_number, numericality: { less_than_or_equal_to: 9_999_999_999,
                                             message: 'Phone number must be 10 digits long' }

    validates :address_street, presence: { message: 'Street address is required' },
                               format: { with: /\A[^@]*\z/, message: 'Please enter your street address' }

    validates :city, presence: { message: 'City is required' },
                     format: { with: /\A[^0-9]*\z/, message: 'City cannot include digits' }

    validates :state, presence: { message: 'State is required' }

    validates :zip_code, presence: { message: 'Zip Code is required' },
                         length: { is: 5, message: 'A 5 digit Zip Code is required' },
                         format: { with: /\A[0-9]{5}\z/, message: 'Zip Code can only include digits' }

    validate :validate_no_po_box_in_address

    with_options if: :married? do
      validates :spouse_first_name, presence: { message: 'First Name is required' },
                                    format: { with: /\A[^0-9]*\z/, message: 'First Name cannot include digits' }
      validates :spouse_last_name, presence: { message: 'Last Name is required' },
                                   format: { with: /\A[^0-9]*\z/, message: 'Last Name cannot include digits' }
    end

    with_options if: :spouse_different_address? do
      validates :spouse_address_street, presence: { message: 'Address is required' }
      validates :spouse_city, presence: { message: 'City is required' },
                              format: { with: /\A[^0-9]*\z/, message: 'City cannot include digits' }
      validates :spouse_state, presence: { message: 'State is required' }
      validates :spouse_zip_code, presence: { message: 'Zip code is required' },
                                  length: { is: 5, message: 'A 5 digit Zip Code is required' },
                                  format: { with: /\A[0-9]{5}\z/, message: 'Zip Code can only include digits' }

      validate :validate_no_po_box_in_spouse_address
    end

    def initialize(attributes = {})
      super

      clear_spouse_name_if_needed
      clear_spouse_address_if_needed
    end

    def pi1_attributes
      {
        address_street:,
        city:,
        first_name:,
        last_name:,
        password:,
        phone_number:,
        state:,
        tcpa_accepted:,
        zip_code:
      }
    end

    def spouse_attributes
      address = attributes.slice('address_street', 'city', 'state', 'zip_code') if married && !spouse_different_address

      {
        married:,
        first_name: spouse_first_name,
        last_name: spouse_last_name,
        address_street: spouse_address_street,
        city: spouse_city,
        state: spouse_state,
        zip_code: spouse_zip_code
      }.merge(address&.symbolize_keys || {})
    end

    private

    def clear_spouse_name_if_needed
      return if married?

      self.spouse_first_name = nil
      self.spouse_last_name = nil
      self.spouse_different_address = false
    end

    def clear_spouse_address_if_needed
      return if spouse_different_address?

      self.spouse_address_street = nil
      self.spouse_city = nil
      self.spouse_state = nil
      self.spouse_zip_code = nil
    end

    def validate_no_po_box_in_address
      return unless contains_po_box?(address_street)

      errors.add(:address_street, 'Please provide a valid residential address. P.O. Boxes are not accepted.')
    end

    def validate_no_po_box_in_spouse_address
      return unless contains_po_box?(spouse_address_street)

      errors.add(:spouse_address_street, 'Please provide a valid residential address. P.O. Boxes are not accepted.')
    end

    def contains_po_box?(value)
      value.to_s.gsub(/[[:punct:]]|[[:space:]]/, '').downcase.include?('pobox')
    end

    def married? = married
    def spouse_different_address? = spouse_different_address
  end
end
