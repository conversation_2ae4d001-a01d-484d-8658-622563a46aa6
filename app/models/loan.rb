# frozen_string_literal: true

# == Schema Information
#
# Table name: loans
#
#  id                             :uuid             not null, primary key
#  adverse_action_sent            :timestamptz
#  affiliate_sharing              :boolean
#  amount                         :decimal(15, 2)   not null
#  anual_income                   :decimal(15, 2)
#  arix_onboarding_status         :string
#  bankruptcy_filed_date          :date
#  beyond_salesforce              :text
#  campaign                       :string(255)
#  code                           :string(100)
#  contract_signing_token         :uuid
#  crb_dry_run                    :boolean          default(FALSE), not null
#  credit_inquiries_last_6_months :integer
#  credit_score                   :decimal(8, 2)
#  credit_score_range             :string(20)
#  decision_reason_number         :string(100)
#  decline_reason_text            :text
#  decline_reasons                :jsonb
#  deleted_at                     :timestamptz
#  dti                            :decimal(16, 8)   default(0.0), not null
#  education_level                :string(100)
#  employment_industry            :string(150)
#  employment_pay_frecuency       :string(100)
#  employment_start_date          :string(50)
#  employment_status              :string(20)
#  exclude_from_allocation        :boolean          default(FALSE), not null
#  excluded_from_concentration_at :datetime
#  expiration_date                :timestamptz
#  housing_status                 :string(50)
#  last_paycheck_on               :date
#  monthly_deposit_amount         :decimal(15, 2)
#  monthly_housing_payment        :decimal(50, 2)
#  originating_party              :string(50)
#  product_type                   :string(100)      default("PPC")
#  program_duration_in_tmonths    :integer
#  purpose                        :string(50)       default("debt_consolidation")
#  requested_offers               :boolean          default(FALSE)
#  score_factor                   :text
#  screened_for_bankruptcy        :boolean          default(FALSE), not null
#  should_send_adverse_action     :boolean          default(FALSE), not null
#  source_type                    :string(10)
#  time_at_residence              :string(20)
#  verified_dti                   :decimal(16, 8)
#  verified_income                :decimal(15, 2)
#  verified_income_ratio          :decimal(16, 8)
#  created_at                     :timestamptz      not null
#  updated_at                     :timestamptz
#  affiliate_lead_id              :text
#  borrower_id                    :uuid
#  investor_id                    :uuid
#  loan_app_status_id             :integer
#  program_id                     :string(12)
#  request_id                     :string(100)
#  unified_id                     :string(255)
#
# Indexes
#
#  index_loans_on_contract_signing_token          (contract_signing_token) UNIQUE
#  index_loans_on_excluded_from_concentration_at  (excluded_from_concentration_at)
#  index_loans_on_investor_id                     (investor_id)
#  index_loans_on_loan_app_status_id              (loan_app_status_id)
#  index_loans_on_program_id                      (program_id)
#  loans_borrower_id_idx                          (borrower_id)
#  loans_code_uppercase_idx                       (upper((code)::text))
#  loans_product_type_idx                         (product_type)
#  loans_request_id_unique                        (request_id) UNIQUE
#  loans_unified_id_unique                        (unified_id) UNIQUE
#
# Foreign Keys
#
#  FK-borrower-loans   (borrower_id => borrowers.id)
#  FK-loan-app-status  (loan_app_status_id => loan_app_statuses.id)
#  FK-loan-owner       (investor_id => investors.id)
#

# The following attributes are expected to be deprecated and should not be used:
# - expiration_date: Use the EXPIRED LoanAppStatus instead.
class Loan < ApplicationRecord # rubocop:disable Metrics/ClassLength
  include Notifier
  include Broadcastable

  IPL_CASH_BACK_LOAN_PRODUCT_TYPE = 'IPL_Cash_Back'
  IPL_LOAN_PRODUCT_TYPE = Lead::TYPES[:IPL].freeze
  UPL_LOAN_PRODUCT_TYPE = Lead::TYPES[:UPL].freeze
  DIRECT_MAIL_PRODUCT_TYPES = Lead::TYPES.values_at(:DM, :UPL).freeze
  WITHDRAWAL_ALLOWED_TYPES  = Lead::TYPES.values_at(:DM, :IPL, :UPL).freeze
  SOURCE_TYPES = [
    BEYOND_SOURCE_TYPE = 'BEYOND',
    PHONE_SOURCE_TYPE = 'GDS',
    WEB_SOURCE_TYPE = 'WEB'
  ].freeze

  belongs_to :loan_app_status, class_name: '::LoanAppStatus'
  belongs_to :borrower, class_name: 'Borrower'
  belongs_to :investor, class_name: 'Investor', optional: true
  has_many :offers, class_name: '::Offer'
  has_many :borrower_additional_infos, class_name: 'BorrowerAdditionalInfo'
  has_one :arix_funding_status, class_name: 'ArixFundingStatus'
  has_one :latest_offer, -> { latest_first }, class_name: '::Offer'
  has_one :loan_detail, class_name: '::LoanDetail'
  has_one :loan_inquiry, -> { order(created_at: :desc) }, class_name: '::LoanInquiry'
  has_one :loan_payment_detail, class_name: '::LoanPaymentDetail'
  has_many :loan_tradeline_details, lambda {
    order(:tradeline_estimated_settlement_amount, :tradeline_name)
  }, class_name: '::LoanTradelineDetail'
  has_many :loan_status_histories, -> { order(updated_at: :desc) }, class_name: '::LoanStatusHistory'
  has_one :loanpro_loan, -> { where(deleted_at: nil).order(created_at: :desc) }, class_name: '::LoanproLoan'
  has_many :loanpro_loans, -> { order(created_at: :desc) }, class_name: '::LoanproLoan'
  has_many :til_histories, -> { where(deleted_at: nil).order(created_at: :desc) }, class_name: 'TilHistory'
  has_many :bank_accounts, class_name: 'BankAccount'
  has_many :todos, -> { order(:id) }, class_name: 'Todo'
  has_many :docs, -> { order(:id) }, class_name: 'Doc'
  has_many :plaid_reports, -> { order(created_at: :desc) }, class_name: 'PlaidReport'
  has_many :ocrolus_reports, -> { order(created_at: :desc) }, class_name: 'OcrolusReport'
  has_one :contact_preference, class_name: 'ContactPreference'
  has_one :socure_monitoring, class_name: 'SocureMonitoring'

  # Add ProxyRequest to allow a hook for 3rd Party Requests and loan_status_history
  attr_accessor :bypass_beyond_status_update, :bypass_loan_status_history

  after_save :track_loan_status_history, if: :saved_change_to_loan_app_status_id?

  after_update :update_arix_funding_status_submission_status, if: :saved_change_to_arix_onboarding_status?

  # This must be after commit, otherwise the job can run before a create or update is committed

  after_commit :update_beyond_loan_status, if: :propagate_status_update_to_beyond?
  after_commit :manage_dropoff_list, if: :saved_change_to_loan_app_status_id?, on: :update
  after_update_commit :broadcast, if: :saved_change_to_loan_app_status_id?

  enum :education_level,
       %w[high_school associates bachelors masters other_graduate other].index_by(&:to_sym),
       prefix: true

  enum :employment_status,
       %w[employed_full_time employed_part_time military not_employed self_employed retired
          other].index_by(&:to_sym),
       prefix: true

  enum :time_at_residence, %w[less_than_1_year 1_to_2_years more_than_3_years].index_by(&:to_sym),
       prefix: true

  enum :employment_industry,
       %w[agriculture_or_farming construction education energy financial_services government_or_public_services
          health_services information_technology manufacturing professional_or_business_services
          transportation_or_utilities wholesale_or_retail_trade entertainment_or_events travel_leisure_or_hospitality
          restaurant retail].index_by(&:to_sym),
       prefix: true

  enum :credit_score_range, %w[excellent good fair poor limited].index_by(&:to_sym),
       prefix: true

  EMPLOYMENT_PAY_FREQUENCIES = [
    WEEKLY_PAY_FREQUENCY = 'weekly',
    BI_WEEKLY_PAY_FREQUENCY = 'biweekly',
    MONTHLY_PAY_FREQUENCY = 'monthly',
    SEMI_MONTHLY_PAY_FREQUENCY = 'semi_monthly'
  ].freeze

  # FYI, this is a known misspelling of "frecuency" that's not super easy to fix
  # because it's drive by a misspelling on the column that supports this attribute
  enum :employment_pay_frecuency, EMPLOYMENT_PAY_FREQUENCIES.index_by(&:to_sym),
       prefix: true

  enum :housing_status, %w[rent own_outright own_with_mortgage].index_by(&:to_sym),
       prefix: true

  ORIGINATING_PARTIES = {
    DIRECT_LICENSES: 'DIRECT_LICENSES',
    CRB: 'CRB'
  }.freeze

  POSSIBLE_EXPIRED_STATUSES = LoanAppStatus::POSSIBLE_EXPIRED_STATUSES + %w[NO_OFFERS]

  enum :originating_party, ORIGINATING_PARTIES

  scope :expirable_status, -> { joins(:loan_app_status).where(loan_app_status: { name: POSSIBLE_EXPIRED_STATUSES }) }
  scope :latest_active_for_borrower, lambda { |borrower|
    joins(:loan_app_status)
      .joins(:borrower)
      .where(
        loan_app_statuses: { name: ::LoanAppStatus::ACTIVE_STATUS },
        borrower: { id: borrower.id },
        deleted_at: nil
      )
      .order(created_at: :desc)
  }
  scope :with_code, ->(code) { where('UPPER(code) = UPPER(?)', code) }

  # Internal statues - loan.arix_onboarding_status
  ARIX_ONBOARDING_STATUSES = [
    READY_TO_PROCESS = 'ReadyToProcess',
    IN_ARIX_LOAN_CREATION = 'InArixLoanCreation',
    IN_DOCUMENT_COLLECTION = 'InDocumentCollection',
    IN_DOCUMENT_PACKAGING = 'InDocumentPackaging',
    IN_DOCUMENT_UPLOADING = 'InDocumentUploading',
    COMPLETED = 'Completed',
    IN_VALIDATION = 'InValidation',
    READY_FOR_ALLOCATION = 'ReadyForAllocation',

    IN_ARIX_LOAN_UPDATE = 'InArixLoanUpdate'
  ].freeze

  # External statuses (given by Arix) - arix_funding_statuses.funding_status
  # See: ArixOnboarding::Webhooks::UpdateFundingStatus::ARIX_LOAN_STATUS_MAP
  ARIX_FUNDING_STATUSES = [
    RECEIVED = 'Received',
    DOCS_COMPLETE = 'DocsComplete',
    PASSED_COMPLIANCE = 'PassedCompliance',
    APPROVED = 'Approved',
    IN_FUNDING = 'InFunding',
    FUNDED = 'Funded',
    ACCOUNTING_ENTRIES_COMPLETE = 'AccountingEntriesComplete',
    READY_TO_SELL = 'ReadyToSell',
    SOLD = 'Sold',
    SETTLED = 'Settled',
    AWAITING_FUNDING = 'AwaitingFunding',
    IN_REVERSE = 'InReverse',
    ACCOUNTING_ENTRIES_REVERSED = 'AccountingEntriesReversed',
    CANCELLED = 'Cancelled',
    COMPLIANCE_FAILED = 'ComplianceFailed',
    REJECTED = 'Rejected',
    RETURNED = 'Returned',
    NOT_FULLY_FUNDED = 'NotFullyFunded'
  ].freeze

  scope :eligible_for_arix_onboarding, lambda {
                                         joins(:investor, :loan_app_status)
                                           .where(
                                             loan_app_status: ::LoanAppStatus.for('ONBOARDED')
                                           )
                                           .where(arix_onboarding_status: nil)
                                           .where.not(investor_id: nil)
                                       }

  def program_id_count
    Loan.where(program_id:).count
  end

  def lead
    return @lead if defined?(@lead)

    @lead = Lead.with_code(code).where(deleted_at: nil).first
  end

  # Retrieves the latest selected offer. Ensures only the most recent is fetched,
  # handling the scenario when multiple offers marked as selected.
  def selected_offer
    offers.where(selected: true).order(created_at: :desc).first
  end

  def seconds_left
    return 0 unless latest_offer && expirable_status?

    now = Time.current
    latest_offer.expiration_date - now
  end

  def track_loan_status_history
    return if bypass_loan_status_history

    old_status = format_status_name(LoanAppStatus.find_by_id(attribute_before_last_save(:loan_app_status_id))&.name)
    new_status = format_status_name(loan_app_status&.name)

    LoanStatusHistory.create(id: SecureRandom.uuid, new_status:, old_status:, loan_id: id, request_id:, unified_id:,
                             updated_at: Time.now)
  end

  def update_arix_funding_status_submission_status
    build_arix_funding_status unless arix_funding_status
    arix_funding_status.update!(submission_status: arix_onboarding_status)
  end

  def update_beyond_loan_status
    return unless ipl?

    Rails.logger.info('ams_hook_loan_status_update')

    status_update_data = {
      'loan_id' => id,
      'loan_app_status' => loan_app_status&.name,
      'updated_at' => updated_at.to_s
    }

    ::Beyond::LoanStatusUpdateJob.perform_async(status_update_data)
  end

  def manage_dropoff_list
    return unless ipl?

    Talkdesk::OrchestrateCallbackJob.perform_async(id)
  end

  def format_status_name(status)
    return '' if status.blank?

    "#{product_type}_#{status}"
  end

  def beyond_status_name
    format_status_name(loan_app_status&.name)
  end

  def hours_left
    return 0 unless latest_offer && expirable_status?

    (seconds_left / 3600).to_i
  end

  # Calculates the term_frequency from Offers, Employment, or Beyond's payment
  # frequency.
  def term_frequency
    offer_frequency = selected_offer&.term_frequency || term_frequency_from_upl_inquiry
    beyond_payment_frequency = nil
    if offer_frequency
      offer_frequency
    elsif employment_pay_frecuency
      employment_pay_frecuency == 'weekly' ? 'biweekly' : employment_pay_frecuency
    else
      beyond_payment_frequency
    end
  end

  def term_frequency_from_upl_inquiry
    return unless upl?

    loan_inquiry&.offers&.dig(0, 'payment_frequency')
  end

  def active?
    return false unless loan_app_status

    ::LoanAppStatus::ACTIVE_STATUS.include?(loan_app_status.name)
  end

  def expired?
    return false if loan_app_status.name == 'NO_OFFERS'

    ::LoanAppStatus::EXPIRED_STATUSES.include?(loan_app_status.name) ||
      (expirable_status? && seconds_left < 1)
  end

  def expirable_status?
    ::LoanAppStatus::POSSIBLE_EXPIRED_STATUSES.include?(loan_app_status.name)
  end

  def actual_borrower_additional_info
    borrower_additional_infos.where(borrower_id:).order('created_at DESC NULLS LAST').first
  end

  def offers_expired_status?
    POSSIBLE_EXPIRED_STATUSES.include?(loan_app_status.name)
  end

  def equifax_product_type?
    Lead::EQUIFAX_PRODUCT_TYPES.values.include?(product_type)
  end

  def withdrawable?
    WITHDRAWAL_ALLOWED_TYPES.include?(product_type)
  end

  def ipl?
    product_type == IPL_LOAN_PRODUCT_TYPE
  end

  def upl?
    product_type == UPL_LOAN_PRODUCT_TYPE
  end

  def self.current_for_borrower_by_email(email)
    # This query mirrors the way Service Layer looks up the loan for a borrower by the email in many places
    # (https://github.com/Above-Lending/service-layer/blob/c8c2304be5628f0f45db0a5cb3b8951829aac213/storage/loan/index.js#L527).
    joins(:borrower)
      .joins(:loan_app_status)
      .includes(:loan_app_status, :borrower, :loan_tradeline_details, :loan_payment_detail, :latest_offer)
      .where(borrowers: { email: })
      .order(
        Arel.sql(<<-SQL.squish
              CASE loans.product_type
                WHEN 'IPL' THEN '1'
                WHEN 'DM' THEN '2'
                WHEN 'PPC' THEN '3'
                ELSE '4'
              END
        SQL
                ),
        created_at: :desc
      )
      .first
  end

  # WARNING: Using `includes` does not guarantee correct SQL JOIN behavior when sorting by associated records.
  #          You **MUST** explicitly add `.order` clauses for any associated table fields you rely on,
  #          and ensure the correct join behavior with `.joins` or `.includes`) to avoid subtle bugs.
  def self.current_for_borrower_by_id(id)
    status_order_sql = 'CASE WHEN loan_app_statuses.name IN (?) THEN 0 ELSE 1 END'
    active_status_names = ::LoanAppStatus::ACTIVE_STATUS
    sanitized_sql = ActiveRecord::Base.sanitize_sql_array([status_order_sql, active_status_names])

    joins(:borrower, :loan_app_status)
      .includes(
        :loan_app_status,
        :borrower,
        :loan_tradeline_details,
        :loan_payment_detail,
        :latest_offer
      )
      .where(borrowers: { id: })
      .order(Arel.sql(sanitized_sql),
             created_at: :desc,
             offers: { created_at: :desc }).first
  end

  def expire!
    Rails.logger.info("#{self.class.name} - Marking loan #{id} as expired")

    expired_status_name = 'EXPIRED'

    update!(
      should_send_adverse_action: true,
      loan_app_status_id: ::LoanAppStatus.id(expired_status_name),
      decision_reason_number: 0,
      decline_reason_text: ::Offer::EXPIRED_TEXT,
      decline_reasons: [::Offer::EXPIRED_TEXT],
      credit_score: 0,
      score_factor: 0
    )

    ::Loans::SyncStatusJob.perform_async(id)

    ::Loans::DeliverNoticeOfAdverseActionJob.perform_async(id)
  end

  def declined_date
    declined_statuses = ::LoanAppStatus::DECLINED_STATUSES.map { |name| format_status_name(name) }
    loan_status_histories.where(new_status: declined_statuses).first&.updated_at
  end

  def arix_identifier
    # TODO: When NILE-531 is merged, a missing arix_funding_status should be an error
    arix_funding_status&.arix_loan_id || id
  end

  def propagate_status_update_to_beyond?
    return false if bypass_beyond_status_update
    return true if Rails.env.production?

    Flipper.enabled?(:execute_pre_production_beyond_status_update)
  end

  def credit_freeze_active?
    loan_detail&.credit_freeze_active.present?
  end

  def reset_credit_freeze_flag!
    loan_detail.update!(credit_freeze_active: false)
  end

  protected

  # Light: META PROGRAMMING
  # this gives us the ability to call
  # approved?
  # offered?
  # ....
  def method_missing(method_name, *args, &)
    status = method_name.to_s.chomp('?').upcase
    if LoanAppStatus::ID_TO_NAME.include?(status)
      LoanAppStatus::ID_TO_NAME.index(status) == loan_app_status_id
    else
      super
    end
  end

  def respond_to_missing?(method_name, include_private = false)
    status = method_name.to_s.chomp('?').upcase
    LoanAppStatus::ID_TO_NAME.include?(status) || super
  end
end
