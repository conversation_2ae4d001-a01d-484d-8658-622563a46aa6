# frozen_string_literal: true

module Broadcastable
  def broadcast(force_refresh: false)
    channel = "#{broadcast_namespace}:#{id}"
    payload = { meta: { force_refresh: }, payload: broadcast_payload || {} }

    # These log statements are copied from here but upgraded to info level
    # https://github.com/rails/rails/blob/v7.2.2/actioncable/lib/action_cable/server/broadcasting.rb#L51
    #
    ActionCable.server.logger.info { "[ActionCable] Broadcasting to #{channel}: #{payload.inspect.truncate(300)}" }
    ActionCable.server.broadcast(channel, payload)
  end

  def broadcast_payload
    send("reload_#{broadcast_namespace}_broadcast_associations")
    send("#{broadcast_namespace}_broadcast_payload")
  end

  protected

  def broadcast_namespace
    self.class.name.underscore
  end

  private

  def reload_loan_broadcast_associations
    # Reload associations to ensure we have the latest version before broadcasting.
    reload_borrower
    borrower.reload_bank_account
    reload_loanpro_loan
    reload_loan_detail
  end

  def loan_broadcast_payload
    presentable_offer = LoanApplications::PresentableOffer.call(loan: self)

    {
      loan_app_status_id:,
      presentable_offer_id: presentable_offer&.id,
      contract_generated_at: loanpro_loan&.contract_generated_at.present?,
      has_loanpro_loan: loanpro_loan&.present?,
      has_bank_account: borrower.bank_account&.enabled,
      front_end_declined: front_end_declined?,
      credit_freeze_active: credit_freeze_active?
    }
  end

  def reload_todo_broadcast_associations
    # Reload associations to ensure we have the latest version before broadcasting.
    todo_docs.reload
  end

  def todo_broadcast_payload
    {
      status:,
      type:, # Type can be updated via Gds::SyncSingleTask
      auto_verification_in_progress: auto_verification_in_progress?,
      has_rejected_docs: todo_docs.any? { |doc| doc.status.to_sym == :rejected }
    }
  end

  def reload_til_history_broadcast_associations; end

  def til_history_broadcast_payload
    {
      loanpro_loan_external_id:,
      docusign_envelope_id:,
      latest: latest?,
      voided: voided_at?
    }
  end
end
