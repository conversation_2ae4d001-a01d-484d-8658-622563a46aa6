# frozen_string_literal: true

# == Schema Information
#
# Table name: loan_inquiries
#
#  id                         :uuid             not null, primary key
#  application                :jsonb            not null
#  decline                    :jsonb
#  offers                     :jsonb
#  created_at                 :timestamptz      not null
#  updated_at                 :timestamptz
#  beyond_request_tracking_id :string(100)      not null
#  gds_request_id             :string(100)      not null
#  loan_id                    :uuid
#
# Indexes
#
#  index_loan_inquiries_on_loan_id                   (loan_id)
#  loan_inquiries_beyond_request_tracking_id_unique  (beyond_request_tracking_id) UNIQUE
#  loan_inquiries_gds_request_id_unique              (gds_request_id) UNIQUE
#
# Foreign Keys
#
#  FK-loan-loan_inquiries  (loan_id => loans.id)
#
class LoanInquiry < ApplicationRecord
  belongs_to :loan, required: false

  def expired?
    return true if offers.nil? || offers.empty?

    DateTime.parse(offers[0]['expiration_date']) < DateTime.now
  end
end
