# frozen_string_literal: true

# == Schema Information
#
# Table name: loan_tradeline_details
#
#  id                                    :uuid             not null, primary key
#  deleted_at                            :timestamptz
#  original_creditor                     :string(255)
#  settled_tradelined_flag               :string(100)
#  settlement_percent                    :decimal(8, 2)
#  tradeline_account_number              :string(255)
#  tradeline_estimated_settlement_amount :decimal(8, 2)    not null
#  tradeline_name                        :string(255)
#  created_at                            :timestamptz      not null
#  updated_at                            :timestamptz
#  loan_id                               :uuid             not null
#
# Indexes
#
#  index_loan_tradeline_details_on_loan_id  (loan_id)
#
# Foreign Keys
#
#  FK-loan-tradeline_details  (loan_id => loans.id)
#
class LoanTradelineDetail < ApplicationRecord
  belongs_to :loan, required: false
end
