# frozen_string_literal: true

# == Schema Information
#
# Table name: loan_details
#
#  id                                                                               :uuid             not null, primary key
#  amount_financed                                                                  :decimal(8, 2)    not null
#  beyond_enrollment_date                                                           :date
#  beyond_enrollment_status                                                         :string(50)
#  cft_account_details                                                              :string
#  cft_account_holder_name                                                          :string
#  cft_account_number                                                               :string
#  cft_bank_name                                                                    :string
#  consecutive_payments_count                                                       :integer          default(0), not null
#  credit_freeze_active                                                             :boolean          default(FALSE)
#  credit_freeze_first_seen_at                                                      :timestamptz
#  credit_model_level                                                               :string
#  credit_model_score                                                               :decimal(6, 5)
#  decision_challenger(The champion decison value. [OFFERED or FRONT_END_DECLINED]) :string
#  decision_champion(The champion decison value. [OFFERED or FRONT_END_DECLINED])   :string
#  deleted_at                                                                       :timestamptz
#  eligibility_level                                                                :string(1)
#  estimated_beyond_fees                                                            :decimal(8, 2)    default(0.0)
#  estimated_cft_deposits                                                           :decimal(8, 2)
#  hard_inquiries_60_days                                                           :integer
#  hard_inquiries_90_days                                                           :integer
#  lifetime_payment_adherence                                                       :decimal(16, 8)   default(0.0), not null
#  months_since_enrollment                                                          :decimal(8, 2)
#  nsfs_12_months                                                                   :integer          default(0), not null
#  nsfs_18_months                                                                   :integer          default(0), not null
#  nsfs_24_months                                                                   :integer          default(0), not null
#  nsfs_3_months                                                                    :integer
#  nsfs_4_months                                                                    :integer          default(0), not null
#  nsfs_6_months                                                                    :integer          default(0), not null
#  nsfs_9_months                                                                    :integer          default(0), not null
#  nsfs_lifetime                                                                    :integer          default(0), not null
#  payment_adherence_ratio_3_months                                                 :decimal(8, 2)
#  payment_adherence_ratio_4_months                                                 :decimal(8, 2)    default(0.0), not null
#  payment_adherence_ratio_6_months                                                 :decimal(8, 2)
#  total_amount_enrolled_debt                                                       :decimal(8, 2)
#  created_at                                                                       :timestamptz      not null
#  updated_at                                                                       :timestamptz
#  loan_id                                                                          :uuid             not null
#
# Indexes
#
#  index_loan_details_on_loan_id  (loan_id)
#
# Foreign Keys
#
#  FK-loan-details  (loan_id => loans.id)
#
class LoanDetail < ApplicationRecord
  belongs_to :loan, required: false

  after_create_commit -> { loan.broadcast }
  after_update_commit -> { loan.broadcast }, if: :saved_change_to_credit_freeze_active?
end
