# frozen_string_literal: true

class LeadBlueprint < Blueprinter::Base
  PAYMENT_FREQUENCY_MAPPING = {
    'Weekly' => 'weekly',
    'Monthly' => 'monthly',
    'Bi-Weekly' => 'bi_weekly',
    'Semi Monthly' => 'semi_monthly'
  }.freeze

  identifier :id

  fields :first_name,
         :last_name,
         :address,
         :city,
         :state,
         :zip_code,
         :phone_number,
         :code_used,
         :type,
         :code_status,
         :lead_bank_account,
         :service_entity_name

  field :date_of_birth do |lead|
    lead.date_of_birth&.to_time&.utc&.iso8601(3)
  end

  field :lead_bank_account do |lead|
    {
      holder_name: lead.nu_dse_holder_s_name_c,
      bank_name: lead.nu_dse_bank_name_c,
      account_number: lead.nu_dse_bank_account_number_c&.to_s,
      routing_number: lead.nu_dse_routing_number_c&.to_s,
      account_type: lead.nu_dse_account_type_c
    }
  end

  view :extended do
    fields :email,
           :cft_account_details,
           :account_number,
           :loan_details,
           :fico_score,
           :client_id,
           :program_id,
           :months_since_enrollment

    field :payment_details do |lead, options|
      if (options[:app] == ::ExternalApp::GDS) && lead.payment_details
        frequency = PAYMENT_FREQUENCY_MAPPING[lead.payment_details['beyond_payment_frequency']]
        lead.payment_details.merge('beyond_payment_frequency' => frequency)
        # TODO: ASUN-722, should this be updated to selected_offer.term_frequency?
      else
        lead.payment_details
      end
    end

    # TODO: remove this temporary fix
    # once GDS is able to handle the non-nested version of tradeline_details
    field :tradeline_details do |lead|
      if lead.tradeline_details.is_a?(Array)
        { 'tradeline_details' => lead.tradeline_details }
      else
        lead.tradeline_details
      end
    end
  end
end
