.DEFAULT_GOAL := help
SERVICE ?= alp

alp.upgrade: # Upgrade to ALP v2
	@echo Copying compose files for ALP V2
	ln -s .alp/compose/compose.yml ./compose.yml
	ln -s .alp/compose/compose.development.yml ./compose.development.yml
	ln -s .alp/compose/compose.sandbox.yml ./compose.sandbox.yml
	ln -s .alp/compose/compose.staging.yml ./compose.staging.yml

alp.downgrade: # Downgrade to ALP V1
	@echo Removing ALP V2 Compose files
	rm ./compose.yml
	rm ./compose.development.yml
	rm ./compose.sandbox.yml
	rm ./compose.staging.yml
	@echo Removing alp development keys
	rm -f ./local-public.key
	rm -f ./local-private.key
	@echo Removing alp environment variables
	rm .alp/.env/development
	rm .alp/.env/sandbox
	rm .alp/.env/staging

alp.secrets.pull: # Fetches Environment files from Bitwarden
	.alp/bin/pull_secrets

alp.secrets.push: # Pushes Environment files to Bitwarden
	.alp/bin/push_secrets

alp.setup: # setup ALP platform
	.alp/bin/setup

alp.reset: # Resets database and precompiles assets
	docker compose run --rm alp command .alp/bin/ams_setup

alp.up: # Start all ALP Services - deprecated use alp.start
	@echo **Deprecating** Use alp.start
	docker compose stop
	docker compose up -d
	docker attach application_management_system-alp-1

alp.start: # Start all ALP Services
	docker compose stop
	docker compose up -d
	docker attach application_management_system-alp-1

alp.sidekiq: # Like alp.start, but attach to sidekiq instead of rails app
	docker compose stop
	docker compose up -d
	docker attach application_management_system-sidekiq-1

alp.stop: # Stop all ALP Services
	docker compose stop

alp.exec.bash: # Bash prompt on running container
	docker compose exec $(SERVICE) bash

alp.bash: # Bash prompt on running container
	docker compose run --rm $(SERVICE) command bash

alp.migrate: # Run database and seed migrations
	docker compose exec $(SERVICE) bin/rails db:migrate
	docker compose exec $(SERVICE) bin/rails seed:migrate

# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #
 # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #
# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #

bash: # Bash prompt on running container
	docker compose exec $(SERVICE) bash

console: # Rails console for running web container
	docker compose exec $(SERVICE) bin/rails c

lint: # Rubocop linting for all changed files
	docker compose exec $(SERVICE) bash -c "(git diff --name-only --diff-filter=d; git ls-files --others --exclude-standard) | grep '\.rb$ ' | xargs bundle exec rubocop"

lint.fix: # Rubocop linting for all changed files
	docker compose exec $(SERVICE) bash -c "(git diff --name-only --diff-filter=d; git ls-files --others --exclude-standard) | grep '\.rb$ ' | xargs bundle exec rubocop -a"

lint.fix.all: # Rubocop linting for all files
	docker compose exec $(SERVICE) bundle exec rubocop -a .

logs: # Tail the service container's logs
	docker compose exec $(SERVICE) tail -f log/development.log

gems: # Install gems
	docker compose exec $(SERVICE) bundle install

migrate: # Run migrations
	docker compose exec $(SERVICE) bin/rails db:migrate

test.all: # Run the full rspec test suite
	docker compose exec $(SERVICE) bundle exec rspec

test.changed: # Run the rspec test for all changed spec files
	docker compose exec $(SERVICE) bash -c "git diff --name-only --diff-filter=d HEAD | grep -E '^spec/.+\.rb$$' | xargs bundle exec rspec"

test.coverage: # Open the code coverage webpage
	open coverage/index.html

docker.build: # Build containers
	docker compose build

docker.attach: # Attach to a running container
	docker attach $(SERVICE)

docker.ps: # Show running processes
	docker compose ps

docker.restart.alp: # Restart the alp container
	docker compose restart alp vite

docker.stop: # Stop running containers
	docker compose stop

docker.start: # Start stopped containers
	docker compose start

docker.up: # Start containers
	docker compose up -d

docker.up.alp: # Start containers
	docker compose up alp vite -d

docker.build.up: # Build and start containers
	docker compose up --build -d

docker.down: # Bring down the service
	docker compose down

docker.prune: # Prune and free up system file space
	docker system prune --all --force

docker.prune.all: # Prune All (including volumes) and free up system file space
	docker system prune --all --force --volumes

setup.reset: # Completely resets your alp setup
	docker compose down
	bin/alp/reset_docker
	bin/alp/reset_dbs
	bin/alp/setup

vite.watch: # Compiles and watches vite assets
	bin/vite dev

# Show help topics
help:
	@grep -E '^[a-zA-Z0-9_.-]+:.*?# .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?# "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'
